/**
 * MemberProfileEditor 组件相关的事件处理函数
 * 
 * 本文件包含所有事件处理逻辑，包括状态更新、模态框控制、CRUD 操作等，
 * 实现事件处理逻辑与 UI 组件的分离。
 */

import { toast } from "sonner";
import { arrayMove } from "@dnd-kit/sortable";
import type { DragEndEvent } from "@dnd-kit/core";

// 类型导入
import { MemberStatus } from "@prisma/client";
import type {
  Education,
  Award,
  Teaching,
  Project,
  AcademicService,
  Presentation,
  SoftwareDataset,
} from "@prisma/client";
import type {
  EditablePublication,
  EditableProjectInfo,
  SectionId,
  AvatarState,
  ModalStates,
  EditingStates,
  DataListStates,
  LoadingStates,
  BasicFieldStates,
  SectionStates,
} from "./types";

// 工具函数导入
import {
  formatStatusLabel,
  validateAvatarFile,
  createAvatarPreview,
  isValidMemberStatus,
  processInputValue,
  validateNumberInput,
  createFeaturedPublicationsUpdateData,
  findDragItemIndex,
  validateDragIndices,
  parseZodError,
} from "./utils";

// 查询函数导入
import {
  updateMemberFieldQuery,
  updateMemberStatusQuery,
  updateMemberVisibilityQuery,
  uploadAvatarQuery,
  updateFeaturedPublicationsQuery,
  educationQueries,
  awardQueries,
  teachingQueries,
  projectQueries,
  academicServiceQueries,
  presentationQueries,
  softwareDatasetQueries,
} from "./queries";

/**
 * 创建状态处理函数的工厂函数
 * 
 * @param initialData - 初始数据
 * @param states - 各种状态对象
 * @param setters - 状态设置函数对象
 * @returns 事件处理函数对象
 */
export function createHandlers(
  initialData: any,
  states: {
    avatar: AvatarState;
    modal: ModalStates;
    editing: EditingStates;
    dataList: DataListStates;
    loading: LoadingStates;
    basicField: BasicFieldStates;
    section: SectionStates;
  },
  setters: {
    setAvatarUrl: (url: string) => void;
    setAvatarFile: (file: File | null) => void;
    setAvatarPreview: (preview: string | null) => void;
    setIsUploadingAvatar: (loading: boolean) => void;
    setCurrentStatus: (status: MemberStatus | undefined) => void;
    setIsStatusLoading: (loading: boolean) => void;
    setIsPublic: (isPublic: boolean) => void;
    setIsVisibilityLoading: (loading: boolean) => void;
    setOpenSections: (sections: SectionStates | ((prev: SectionStates) => SectionStates)) => void;
    setEducationHistory: (history: Education[] | ((prev: Education[]) => Education[])) => void;
    setAwardsList: (awards: Award[] | ((prev: Award[]) => Award[])) => void;
    setTeachingList: (teaching: Teaching[] | ((prev: Teaching[]) => Teaching[])) => void;
    setProjectsList: (projects: EditableProjectInfo[] | ((prev: EditableProjectInfo[]) => EditableProjectInfo[])) => void;
    setAcademicServicesList: (services: AcademicService[] | ((prev: AcademicService[]) => AcademicService[])) => void;
    setPresentationList: (presentations: Presentation[] | ((prev: Presentation[]) => Presentation[])) => void;
    setSoftwareAndDatasetsList: (datasets: SoftwareDataset[] | ((prev: SoftwareDataset[]) => SoftwareDataset[])) => void;
    setEditablePublications: (publications: EditablePublication[] | ((prev: EditablePublication[]) => EditablePublication[])) => void;
    setIsSavingFeatured: (saving: boolean) => void;
    setIsEducationModalOpen: (open: boolean) => void;
    setEditingEducationData: (data: Partial<Education> | null) => void;
    setIsAwardModalOpen: (open: boolean) => void;
    setEditingAward: (award: Partial<Award> | null) => void;
    setIsTeachingModalOpen: (open: boolean) => void;
    setEditingTeaching: (teaching: Partial<Teaching> | null) => void;
    setIsProjectModalOpen: (open: boolean) => void;
    setEditingProjectData: (data: (Project & { memberRole?: string }) | undefined) => void;
    setIsAcademicServiceModalOpen: (open: boolean) => void;
    setEditingAcademicService: (service: AcademicService | null) => void;
    setIsPresentationModalOpen: (open: boolean) => void;
    setEditingPresentation: (presentation: Presentation | null) => void;
    setIsSoftwareDatasetModalOpen: (open: boolean) => void;
    setEditingSoftwareAndDataset: (dataset: SoftwareDataset | null) => void;
  }
) {
  /**
   * 处理头像文件选择
   */
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 验证文件
    const validation = validateAvatarFile(file);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    setters.setAvatarFile(file);
    setters.setAvatarPreview(createAvatarPreview(file));
  };

  /**
   * 处理头像上传
   */
  const handleUploadAvatar = async () => {
    if (!states.avatar.avatarFile) return;

    setters.setIsUploadingAvatar(true);
    try {
      // 上传头像文件
      const uploadResult = await uploadAvatarQuery(
        states.avatar.avatarFile,
        String(initialData.username || "")
      );

      if (uploadResult.success && uploadResult.url) {
        // 更新数据库中的头像 URL
        const updateResult = await updateMemberFieldQuery(
          initialData.id,
          "avatar_url",
          uploadResult.url
        );

        if (updateResult.success) {
          setters.setAvatarUrl(uploadResult.url);
          setters.setAvatarPreview(null);
          setters.setAvatarFile(null);
          toast.success("Avatar uploaded and saved successfully!");
        } else {
          toast.error(
            "Avatar uploaded, but failed to save avatar URL: " +
              (updateResult.error || "Unknown error")
          );
        }
      } else {
        toast.error(
          "Failed to upload avatar: " + (uploadResult.error || "Unknown error")
        );
      }
    } catch (err: any) {
      toast.error("Avatar upload error: " + (err.message || "Unknown error"));
    } finally {
      setters.setIsUploadingAvatar(false);
    }
  };

  /**
   * 处理状态变更
   */
  const handleStatusChange = async (newStatusValue: string) => {
    // 验证状态值
    if (!isValidMemberStatus(newStatusValue)) {
      toast.error(`Invalid status value: ${newStatusValue}`);
      return;
    }

    const newStatus: MemberStatus = newStatusValue as MemberStatus;
    setters.setIsStatusLoading(true);

    try {
      const result = await updateMemberStatusQuery(initialData.id, newStatus);
      if (result.success) {
        setters.setCurrentStatus(newStatus);
        toast.success(`Status updated to ${formatStatusLabel(newStatus)}.`);
      } else {
        toast.error(
          `Failed to update status: ${result.error || "Unknown error"}`
        );
      }
    } catch (err: any) {
      toast.error(
        `An unexpected error occurred: ${err.message || "Unknown error"}`
      );
    } finally {
      setters.setIsStatusLoading(false);
    }
  };

  /**
   * 处理可见性变更
   */
  const handleVisibilityChange = async (checked: boolean) => {
    setters.setIsVisibilityLoading(true);
    try {
      const result = await updateMemberVisibilityQuery(initialData.id, checked);
      if (result.success) {
        setters.setIsPublic(checked);
        toast.success(
          `Profile visibility updated to ${checked ? "Public" : "Private"}.`
        );
      } else {
        toast.error(
          `Failed to update visibility: ${result.error || "Unknown error"}`
        );
      }
    } catch (err: any) {
      toast.error(
        `An unexpected error occurred: ${err.message || "Unknown error"}`
      );
    } finally {
      setters.setIsVisibilityLoading(false);
    }
  };

  /**
   * 切换区域展开状态
   */
  const toggleSection = (sectionId: SectionId) => {
    setters.setOpenSections((prev) => ({ 
      ...prev, 
      [sectionId]: !prev[sectionId] 
    }));
  };

  /**
   * 处理特色出版物切换
   */
  const handleToggleFeatured = (publicationId: number) => {
    setters.setEditablePublications((currentPubs) =>
      currentPubs.map((pub) =>
        pub.id === publicationId 
          ? { ...pub, isFeatured: !pub.isFeatured } 
          : pub
      )
    );
  };

  /**
   * 保存特色出版物
   */
  const handleSaveFeaturedPublications = async () => {
    setters.setIsSavingFeatured(true);
    
    const featuredUpdates = createFeaturedPublicationsUpdateData(
      states.dataList.editablePublications
    );

    const actionPromise = updateFeaturedPublicationsQuery(
      initialData.id,
      featuredUpdates
    );

    toast.promise(actionPromise, {
      loading: "Saving featured publications list...",
      success: (result) => {
        if (result.success) {
          return "Featured publications updated successfully!";
        } else {
          throw new Error(
            result.error || "Failed to update featured publications."
          );
        }
      },
      error: (err: any) =>
        `Error: ${err.message || "An unknown error occurred"}`,
    });

    try {
      await actionPromise;
    } catch (e) {
      // 错误已由 toast 处理
    } finally {
      setters.setIsSavingFeatured(false);
    }
  };

  /**
   * 处理拖拽结束事件
   */
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    // 确保 'over' 不为 null
    if (over && active.id !== over.id) {
      setters.setEditablePublications((items) => {
        const oldIndex = findDragItemIndex(items, active.id);
        const newIndex = findDragItemIndex(items, over.id);

        // 检查索引是否有效
        if (!validateDragIndices(oldIndex, newIndex)) {
          console.error("Could not find dragged item index in state.");
          return items;
        }

        // 使用 arrayMove 工具进行正确的重新排序
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  /**
   * 教育记录相关处理函数
   */
  const educationHandlers = {
    openAdd: () => {
      setters.setEditingEducationData(null);
      setters.setIsEducationModalOpen(true);
    },

    openEdit: (education: Education) => {
      setters.setEditingEducationData(education);
      setters.setIsEducationModalOpen(true);
    },

    close: () => {
      setters.setIsEducationModalOpen(false);
      setters.setEditingEducationData(null);
    },

    submit: async (data: any, educationId?: number): Promise<void> => {
      const actionPromise = educationId
        ? educationQueries.update(educationId, data)
        : educationQueries.add(initialData.id, data);

      toast.promise(actionPromise, {
        loading: "Saving education record...",
        success: (result: any) => {
          if (result.success && result.education) {
            if (educationId) {
              setters.setEducationHistory((prev: Education[]) =>
                prev.map((edu: Education) =>
                  edu.id === educationId ? result.education! : edu
                )
              );
            } else {
              setters.setEducationHistory((prev: Education[]) => [
                ...prev,
                result.education!,
              ]);
            }
            educationHandlers.close();
            return `Education record ${educationId ? "updated" : "added"} successfully!`;
          } else {
            throw new Error(result.error || "Failed to save education record.");
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },

    delete: async (educationId: number) => {
      const actionPromise = educationQueries.delete(educationId);

      toast.promise(actionPromise, {
        loading: "Deleting education record...",
        success: (result: any) => {
          if (result.success) {
            setters.setEducationHistory((prev: Education[]) =>
              prev.filter((edu: Education) => edu.id !== educationId)
            );
            return "Education record deleted successfully!";
          } else {
            throw new Error(result.error || "Failed to delete education record.");
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },
  };

  /**
   * 获奖记录相关处理函数
   */
  const awardHandlers = {
    openAdd: () => {
      setters.setEditingAward(null);
      setters.setIsAwardModalOpen(true);
    },

    openEdit: (award: Award) => {
      setters.setEditingAward(award);
      setters.setIsAwardModalOpen(true);
    },

    close: () => {
      setters.setIsAwardModalOpen(false);
      setters.setEditingAward(null);
    },

    submit: async (data: any, awardId?: number): Promise<void> => {
      const actionPromise = awardId
        ? awardQueries.update(awardId, data)
        : awardQueries.add(initialData.id, data);

      toast.promise(actionPromise, {
        loading: "Saving award record...",
        success: (result: any) => {
          if (result.success && result.award) {
            if (awardId) {
              setters.setAwardsList((prev) =>
                prev.map((award) =>
                  award.id === awardId ? result.award! : award
                )
              );
            } else {
              setters.setAwardsList((prev) => [...prev, result.award!]);
            }
            awardHandlers.close();
            return `Award record ${awardId ? "updated" : "added"} successfully!`;
          } else {
            throw new Error(result.error || "Failed to save award record.");
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },

    delete: async (awardId: number) => {
      const actionPromise = awardQueries.delete(awardId);

      toast.promise(actionPromise, {
        loading: "Deleting award record...",
        success: (result: any) => {
          if (result.success) {
            setters.setAwardsList((prev) =>
              prev.filter((award) => award.id !== awardId)
            );
            return "Award record deleted successfully!";
          } else {
            throw new Error(result.error || "Failed to delete award record.");
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },
  };

  return {
    // 头像相关处理函数
    handleAvatarChange,
    handleUploadAvatar,

    // 基本字段处理函数
    handleStatusChange,
    handleVisibilityChange,

    // UI 控制函数
    toggleSection,

    // 出版物相关处理函数
    handleToggleFeatured,
    handleSaveFeaturedPublications,
    handleDragEnd,

    // CRUD 操作处理函数
    education: educationHandlers,
    award: awardHandlers,
  };
}

/**
 * 创建教学记录处理函数
 */
export function createTeachingHandlers(
  initialData: any,
  setters: {
    setIsTeachingModalOpen: (open: boolean) => void;
    setEditingTeaching: (teaching: Partial<Teaching> | null) => void;
    setTeachingList: (teaching: Teaching[] | ((prev: Teaching[]) => Teaching[])) => void;
  }
) {
  return {
    openAdd: () => {
      setters.setEditingTeaching(null);
      setters.setIsTeachingModalOpen(true);
    },

    openEdit: (teaching: Teaching) => {
      setters.setEditingTeaching(teaching);
      setters.setIsTeachingModalOpen(true);
    },

    close: () => {
      setters.setIsTeachingModalOpen(false);
      setters.setEditingTeaching(null);
    },

    submit: async (data: any, teachingId?: number): Promise<void> => {
      const actionPromise = teachingId
        ? teachingQueries.update(teachingId, data)
        : teachingQueries.add(initialData.id, data);

      toast.promise(actionPromise, {
        loading: "Saving teaching record...",
        success: (result: any) => {
          if (result.success && result.teaching) {
            if (teachingId) {
              setters.setTeachingList((prev) =>
                prev.map((teach) =>
                  teach.id === teachingId ? result.teaching! : teach
                )
              );
            } else {
              setters.setTeachingList((prev) => [...prev, result.teaching!]);
            }
            return `Teaching record ${teachingId ? "updated" : "added"} successfully!`;
          } else {
            throw new Error(result.error || "Failed to save teaching record.");
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },

    delete: async (teachingId: number) => {
      const actionPromise = teachingQueries.delete(teachingId);

      toast.promise(actionPromise, {
        loading: "Deleting teaching record...",
        success: (result: any) => {
          if (result.success) {
            setters.setTeachingList((prev) =>
              prev.filter((teach) => teach.id !== teachingId)
            );
            return "Teaching record deleted successfully!";
          } else {
            throw new Error(result.error || "Failed to delete teaching record.");
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },
  };
}

/**
 * 创建项目记录处理函数
 */
export function createProjectHandlers(
  initialData: any,
  setters: {
    setIsProjectModalOpen: (open: boolean) => void;
    setEditingProjectData: (data: (Project & { memberRole?: string }) | undefined) => void;
    setProjectsList: (projects: EditableProjectInfo[] | ((prev: EditableProjectInfo[]) => EditableProjectInfo[])) => void;
  }
) {
  return {
    openAdd: () => {
      setters.setEditingProjectData(undefined);
      setters.setIsProjectModalOpen(true);
    },

    openEdit: (projectInfo: EditableProjectInfo) => {
      setters.setEditingProjectData({
        ...projectInfo.project,
        memberRole: projectInfo.role ?? undefined,
      });
      setters.setIsProjectModalOpen(true);
    },

    close: () => {
      setters.setIsProjectModalOpen(false);
      setters.setEditingProjectData(undefined);
    },

    submit: async (data: any, projectId?: number): Promise<void> => {
      const actionToPerform = () =>
        projectId
          ? projectQueries.update(projectId, data)
          : projectQueries.add(initialData.id, data);

      toast.promise(actionToPerform, {
        loading: "Saving project record...",
        success: (result: any) => {
          if (result.success && result.project) {
            const savedProject = result.project as Project;

            if (projectId) {
              setters.setProjectsList((prev) =>
                prev.map((pm) =>
                  pm.project_id === projectId
                    ? {
                        ...pm,
                        project: { ...pm.project, ...savedProject },
                        role: data.role ?? pm.role,
                      }
                    : pm
                )
              );
            } else {
              const newEditableProject: EditableProjectInfo = {
                project_id: savedProject.id,
                member_id: initialData.id,
                role: data.role ?? null,
                project: savedProject,
              };
              setters.setProjectsList((prev) => [...prev, newEditableProject]);
            }
            return `Project record ${projectId ? "updated" : "added"} successfully!`;
          } else {
            throw new Error(result.error || "Failed to save project record.");
          }
        },
        error: (err: any) => parseZodError(err.message),
      });
    },

    delete: async (projectId: number, memberId: string) => {
      const actionPromise = projectQueries.delete(projectId, memberId);

      toast.promise(actionPromise, {
        loading: "Deleting project record...",
        success: (result: any) => {
          if (result.success) {
            setters.setProjectsList((prev) =>
              prev.filter(
                (pm) =>
                  !(pm.project_id === projectId && pm.member_id === memberId)
              )
            );
            return "Project record deleted successfully!";
          } else {
            throw new Error(result.error || "Failed to delete project record.");
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },
  };
}

/**
 * 创建学术服务记录处理函数
 */
export function createAcademicServiceHandlers(
  initialData: any,
  setters: {
    setIsAcademicServiceModalOpen: (open: boolean) => void;
    setEditingAcademicService: (service: AcademicService | null) => void;
    setAcademicServicesList: (services: AcademicService[] | ((prev: AcademicService[]) => AcademicService[])) => void;
  }
) {
  return {
    openAdd: () => {
      setters.setEditingAcademicService(null);
      setters.setIsAcademicServiceModalOpen(true);
    },

    openEdit: (service: AcademicService) => {
      setters.setEditingAcademicService(service);
      setters.setIsAcademicServiceModalOpen(true);
    },

    close: () => {
      setters.setIsAcademicServiceModalOpen(false);
      setters.setEditingAcademicService(null);
    },

    submit: async (data: any, serviceId?: number): Promise<void> => {
      const actionPromise = serviceId
        ? academicServiceQueries.update(serviceId, data)
        : academicServiceQueries.add(initialData.id, data);

      toast.promise(actionPromise, {
        loading: "Saving academic service record...",
        success: (result: any) => {
          if (result.success && result.academicService) {
            if (serviceId) {
              setters.setAcademicServicesList((prev) =>
                prev.map((svc) =>
                  svc.id === serviceId ? result.academicService! : svc
                )
              );
            } else {
              setters.setAcademicServicesList((prev) => [
                ...prev,
                result.academicService!,
              ]);
            }
            return `Academic service record ${serviceId ? "updated" : "added"} successfully!`;
          } else {
            throw new Error(
              result.error || "Failed to save academic service record."
            );
          }
        },
        error: (err: any) => parseZodError(err.message),
      });
    },

    delete: async (serviceId: number) => {
      const actionPromise = academicServiceQueries.delete(serviceId);

      toast.promise(actionPromise, {
        loading: "Deleting academic service record...",
        success: (result: any) => {
          if (result.success) {
            setters.setAcademicServicesList((prev) =>
              prev.filter((svc) => svc.id !== serviceId)
            );
            return "Academic service record deleted successfully!";
          } else {
            throw new Error(
              result.error || "Failed to delete academic service record."
            );
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },
  };
}

/**
 * 创建演讲记录处理函数
 */
export function createPresentationHandlers(
  initialData: any,
  setters: {
    setIsPresentationModalOpen: (open: boolean) => void;
    setEditingPresentation: (presentation: Presentation | null) => void;
    setPresentationList: (presentations: Presentation[] | ((prev: Presentation[]) => Presentation[])) => void;
  }
) {
  return {
    openAdd: () => {
      setters.setEditingPresentation(null);
      setters.setIsPresentationModalOpen(true);
    },

    openEdit: (presentation: Presentation) => {
      setters.setEditingPresentation(presentation);
      setters.setIsPresentationModalOpen(true);
    },

    close: () => {
      setters.setIsPresentationModalOpen(false);
      setters.setEditingPresentation(null);
    },

    submit: async (data: any, presentationId?: number): Promise<void> => {
      const actionPromise = presentationId
        ? presentationQueries.update(presentationId, data)
        : presentationQueries.add(initialData.id, data);

      toast.promise(actionPromise, {
        loading: "Saving presentation record...",
        success: (result: any) => {
          if (result.success && result.presentation) {
            if (presentationId) {
              setters.setPresentationList((prev) =>
                prev.map((pres) =>
                  pres.id === presentationId ? result.presentation! : pres
                )
              );
            } else {
              setters.setPresentationList((prev) => [...prev, result.presentation!]);
            }
            return `Presentation record ${presentationId ? "updated" : "added"} successfully!`;
          } else {
            throw new Error(
              result.error || "Failed to save presentation record."
            );
          }
        },
        error: (err: any) => parseZodError(err.message),
      });
    },

    delete: async (presentationId: number) => {
      const actionPromise = presentationQueries.delete(presentationId);

      toast.promise(actionPromise, {
        loading: "Deleting presentation record...",
        success: (result: any) => {
          if (result.success) {
            setters.setPresentationList((prev) =>
              prev.filter((pres) => pres.id !== presentationId)
            );
            return "Presentation record deleted successfully!";
          } else {
            throw new Error(
              result.error || "Failed to delete presentation record."
            );
          }
        },
        error: (err: any) =>
          `Error: ${err.message || "An unknown error occurred"}`,
      });
    },
  };
}

/**
 * 创建软件与数据集记录处理函数
 */
export function createSoftwareDatasetHandlers(
  initialData: any,
  setters: {
    setIsSoftwareDatasetModalOpen: (open: boolean) => void;
    setEditingSoftwareAndDataset: (dataset: SoftwareDataset | null) => void;
    setSoftwareAndDatasetsList: (datasets: SoftwareDataset[] | ((prev: SoftwareDataset[]) => SoftwareDataset[])) => void;
  }
) {
  return {
    openAdd: () => {
      setters.setEditingSoftwareAndDataset(null);
      setters.setIsSoftwareDatasetModalOpen(true);
    },

    openEdit: (record: SoftwareDataset) => {
      setters.setEditingSoftwareAndDataset(record);
      setters.setIsSoftwareDatasetModalOpen(true);
    },

    close: () => {
      setters.setIsSoftwareDatasetModalOpen(false);
      setters.setEditingSoftwareAndDataset(null);
    },

    submit: async (
      data: any,
      editingRecord?: SoftwareDataset | null
    ): Promise<{
      success: boolean;
      error?: string;
      softwareDataset?: SoftwareDataset;
    }> => {
      const isEditing = !!editingRecord;
      const action = isEditing
        ? softwareDatasetQueries.update(editingRecord!.id, data)
        : softwareDatasetQueries.add(initialData.id, data);

      const promise = action
        .then((result) => {
          if (!result.success || !result.softwareDataset) {
            throw new Error(
              result.error || "Operation failed or no data returned."
            );
          }

          if (isEditing) {
            setters.setSoftwareAndDatasetsList((prev) =>
              prev.map((item) =>
                item.id === result.softwareDataset!.id
                  ? result.softwareDataset!
                  : item
              )
            );
          } else {
            setters.setSoftwareAndDatasetsList((prev) => [
              ...prev,
              result.softwareDataset!,
            ]);
          }
          return result;
        })
        .catch((err) => {
          console.error("Action failed:", err);
          throw new Error(err.message || "An unexpected error occurred");
        });

      toast.promise(promise, {
        loading: isEditing ? "Updating record..." : "Adding record...",
        success: isEditing
          ? "Record updated successfully!"
          : "Record added successfully!",
        error: (err: Error) => `Error: ${err.message}`,
      });

      return promise;
    },

    delete: async (id: number) => {
      if (!confirm("Are you sure you want to delete this record?")) {
        return;
      }

      const promise = softwareDatasetQueries.delete(id)
        .then((result) => {
          if (!result.success) {
            throw new Error(result.error || "Failed to delete record.");
          }
          setters.setSoftwareAndDatasetsList((prev) =>
            prev.filter((item) => item.id !== id)
          );
          return result;
        })
        .catch((err) => {
          console.error("Delete failed:", err);
          throw new Error(
            err.message || "An unexpected error occurred during deletion."
          );
        });

      toast.promise(promise, {
        loading: "Deleting record...",
        success: "Record deleted successfully!",
        error: (err: Error) => `Error: ${err.message}`,
      });

      try {
        await promise;
      } catch (error) {
        console.error("Final catch for delete error:", error);
      }
    },
  };
}
