/**
 * MemberProfileEditor 组件相关的数据查询和 API 调用函数
 * 
 * 本文件包含所有与后端交互的函数，包括 Server Actions 调用和 API 请求，
 * 实现数据层与 UI 层的分离。
 */

import { toast } from "sonner";

// Server Actions 导入
import {
  updateMemberField,
  updateMemberStatus,
  updateMemberProfileVisibility,
} from "@/app/actions/memberActions";
import {
  addEducationRecord,
  updateEducationRecord,
  deleteEducationRecord,
  type EducationFormData,
} from "@/app/actions/educationActions";
import {
  addAwardRecord,
  updateAwardRecord,
  deleteAwardRecord,
  type AwardFormData,
} from "@/app/actions/awardActions";
import {
  addTeachingRecord,
  updateTeachingRecord,
  deleteTeachingRecord,
  type TeachingFormData,
} from "@/app/actions/teachingActions";
import { updateFeaturedPublications } from "@/app/actions/publicationActions";
import {
  addProjectRecord,
  updateProjectRecord,
  deleteProjectRecord,
  type ProjectFormData,
} from "@/app/actions/projectActions";
import {
  addAcademicServiceRecord,
  updateAcademicServiceRecord,
  deleteAcademicServiceRecord,
  type AcademicServiceFormData,
} from "@/app/actions/academicServiceActions";
import {
  addPresentationRecord,
  updatePresentationRecord,
  deletePresentationRecord,
  type PresentationFormData,
} from "@/app/actions/presentationActions";
import {
  addSoftwareDatasetRecord,
  updateSoftwareDatasetRecord,
  deleteSoftwareDatasetRecord,
  type SoftwareDatasetFormData,
} from "@/app/actions/softwareDatasetActions";

// 类型导入
import { MemberStatus } from "@prisma/client";
import type { MemberProfileData } from "@/lib/types";

/**
 * 更新成员字段
 * 调用 Server Action 更新成员的指定字段
 * 
 * @param memberId - 成员 ID
 * @param fieldName - 字段名称
 * @param value - 新值
 * @returns 更新结果
 */
export async function updateMemberFieldQuery(
  memberId: string,
  fieldName: keyof MemberProfileData,
  value: string | null
) {
  try {
    const result = await updateMemberField(memberId, fieldName as any, value);
    return result;
  } catch (error: any) {
    console.error(`Error updating ${fieldName}:`, error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error.message || "Unknown error"}`
    };
  }
}

/**
 * 更新成员状态
 * 调用 Server Action 更新成员状态
 * 
 * @param memberId - 成员 ID
 * @param newStatus - 新状态
 * @returns 更新结果
 */
export async function updateMemberStatusQuery(
  memberId: string,
  newStatus: MemberStatus
) {
  try {
    const result = await updateMemberStatus(memberId, newStatus);
    return result;
  } catch (error: any) {
    console.error("Error calling updateMemberStatus action:", error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error.message || "Unknown error"}`
    };
  }
}

/**
 * 更新成员资料可见性
 * 调用 Server Action 更新成员资料的公开状态
 * 
 * @param memberId - 成员 ID
 * @param isPublic - 是否公开
 * @returns 更新结果
 */
export async function updateMemberVisibilityQuery(
  memberId: string,
  isPublic: boolean
) {
  try {
    const result = await updateMemberProfileVisibility(memberId, isPublic);
    return result;
  } catch (error: any) {
    console.error("Error calling updateMemberProfileVisibility action:", error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error.message || "Unknown error"}`
    };
  }
}

/**
 * 上传头像文件
 * 调用头像上传 API
 * 
 * @param file - 头像文件
 * @param username - 用户名
 * @returns 上传结果，包含新的头像 URL
 */
export async function uploadAvatarQuery(file: File, username: string) {
  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("username", String(username));

    const response = await fetch("/api/avatar/upload", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();
    return data;
  } catch (error: any) {
    console.error("Avatar upload error:", error);
    return {
      success: false,
      error: error.message || "Unknown error"
    };
  }
}

/**
 * 教育记录相关查询函数
 */
export const educationQueries = {
  /**
   * 添加教育记录
   */
  add: async (memberId: string, data: EducationFormData) => {
    return await addEducationRecord(memberId, data);
  },

  /**
   * 更新教育记录
   */
  update: async (educationId: number, data: EducationFormData) => {
    return await updateEducationRecord(educationId, data);
  },

  /**
   * 删除教育记录
   */
  delete: async (educationId: number) => {
    return await deleteEducationRecord(educationId);
  },
};

/**
 * 获奖记录相关查询函数
 */
export const awardQueries = {
  /**
   * 添加获奖记录
   */
  add: async (memberId: string, data: AwardFormData) => {
    return await addAwardRecord(memberId, data);
  },

  /**
   * 更新获奖记录
   */
  update: async (awardId: number, data: AwardFormData) => {
    return await updateAwardRecord(awardId, data);
  },

  /**
   * 删除获奖记录
   */
  delete: async (awardId: number) => {
    return await deleteAwardRecord(awardId);
  },
};

/**
 * 教学记录相关查询函数
 */
export const teachingQueries = {
  /**
   * 添加教学记录
   */
  add: async (memberId: string, data: TeachingFormData) => {
    return await addTeachingRecord(memberId, data);
  },

  /**
   * 更新教学记录
   */
  update: async (teachingId: number, data: TeachingFormData) => {
    return await updateTeachingRecord(teachingId, data);
  },

  /**
   * 删除教学记录
   */
  delete: async (teachingId: number) => {
    return await deleteTeachingRecord(teachingId);
  },
};

/**
 * 项目记录相关查询函数
 */
export const projectQueries = {
  /**
   * 添加项目记录
   */
  add: async (memberId: string, data: ProjectFormData) => {
    return await addProjectRecord(memberId, data);
  },

  /**
   * 更新项目记录
   */
  update: async (projectId: number, data: ProjectFormData) => {
    return await updateProjectRecord(projectId, data);
  },

  /**
   * 删除项目记录
   */
  delete: async (projectId: number, memberId: string) => {
    return await deleteProjectRecord(projectId, memberId);
  },
};

/**
 * 学术服务记录相关查询函数
 */
export const academicServiceQueries = {
  /**
   * 添加学术服务记录
   */
  add: async (memberId: string, data: AcademicServiceFormData) => {
    return await addAcademicServiceRecord(memberId, data);
  },

  /**
   * 更新学术服务记录
   */
  update: async (serviceId: number, data: AcademicServiceFormData) => {
    return await updateAcademicServiceRecord(serviceId, data);
  },

  /**
   * 删除学术服务记录
   */
  delete: async (serviceId: number) => {
    return await deleteAcademicServiceRecord(serviceId);
  },
};

/**
 * 演讲记录相关查询函数
 */
export const presentationQueries = {
  /**
   * 添加演讲记录
   */
  add: async (memberId: string, data: PresentationFormData) => {
    return await addPresentationRecord(memberId, data);
  },

  /**
   * 更新演讲记录
   */
  update: async (presentationId: number, data: PresentationFormData) => {
    return await updatePresentationRecord(presentationId, data);
  },

  /**
   * 删除演讲记录
   */
  delete: async (presentationId: number) => {
    return await deletePresentationRecord(presentationId);
  },
};

/**
 * 软件与数据集记录相关查询函数
 */
export const softwareDatasetQueries = {
  /**
   * 添加软件与数据集记录
   */
  add: async (memberId: string, data: SoftwareDatasetFormData) => {
    return await addSoftwareDatasetRecord(memberId, data);
  },

  /**
   * 更新软件与数据集记录
   */
  update: async (id: number, data: SoftwareDatasetFormData) => {
    return await updateSoftwareDatasetRecord(id, data);
  },

  /**
   * 删除软件与数据集记录
   */
  delete: async (id: number) => {
    return await deleteSoftwareDatasetRecord(id);
  },
};

/**
 * 更新特色出版物
 * 调用 Server Action 更新特色出版物列表
 * 
 * @param memberId - 成员 ID
 * @param featuredUpdates - 特色出版物更新数据
 * @returns 更新结果
 */
export async function updateFeaturedPublicationsQuery(
  memberId: string,
  featuredUpdates: Array<{
    publicationId: number;
    isFeatured: boolean;
    order: number;
  }>
) {
  try {
    console.log("Sending featured updates:", featuredUpdates);
    const result = await updateFeaturedPublications(memberId, featuredUpdates);
    return result;
  } catch (error: any) {
    console.error("Error updating featured publications:", error);
    return {
      success: false,
      error: error.message || "An unknown error occurred"
    };
  }
}
