/**
 * 可编辑文本字段组件
 * 
 * 提供内联编辑功能的通用文本字段组件，支持文本输入和文本域两种模式。
 * 包含编辑状态管理、数据验证、保存和取消功能。
 */

"use client";

import React, { useState, ChangeEvent } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Save, X, Pencil } from "lucide-react";
import { toast } from "sonner";

// 主题颜色导入
import { themeColors } from "@/styles/theme";

// 类型和工具函数导入
import type { EditableTextFieldProps } from "../types";
import { 
  processInputValue, 
  validateNumberInput, 
  formatDisplayValue 
} from "../utils";
import { updateMemberFieldQuery } from "../queries";

/**
 * 可编辑文本字段组件
 * 
 * @param props - 组件属性
 * @returns React 组件
 */
export function EditableTextField({
  label,
  fieldName,
  initialValue,
  memberId,
  isTextArea = false,
  inputType = "text",
  placeholder,
}: EditableTextFieldProps) {
  // 组件状态
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(formatDisplayValue(initialValue));
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 处理保存操作
   */
  const handleSave = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // 处理输入值
      const processedValue = processInputValue(value, inputType);
      
      // 验证数字输入
      const validation = validateNumberInput(processedValue, inputType);
      if (!validation.isValid) {
        setError(validation.error!);
        setIsLoading(false);
        return;
      }

      // 调用更新 API
      const result = await updateMemberFieldQuery(
        memberId,
        fieldName,
        processedValue
      );

      if (result.success) {
        setIsEditing(false);
        toast.success(`Field "${label}" updated successfully.`);
      } else {
        setError(result.error || "Failed to update field.");
        toast.error(
          `Failed to update "${label}": ${result.error || "Unknown error"}`
        );
      }
    } catch (err: any) {
      console.error(`Error updating ${fieldName}:`, err);
      const errorMessage = `An unexpected error occurred: ${err.message || "Unknown error"}`;
      setError(errorMessage);
      toast.error(`Error updating "${label}": ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 处理取消操作
   */
  const handleCancel = () => {
    setValue(formatDisplayValue(initialValue));
    setIsEditing(false);
    setError(null);
  };

  /**
   * 处理输入框变化
   */
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  };

  /**
   * 处理文本域变化
   */
  const handleTextAreaChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value);
  };

  return (
    <div className="mb-4">
      <Label
        htmlFor={fieldName}
        className={`text-sm font-medium block mb-1 ${themeColors.textColorPrimary} dark:${themeColors.devMutedText}`}
      >
        {label}
      </Label>
      
      {isEditing ? (
        <div className="flex items-center space-x-2">
          {isTextArea ? (
            <Textarea
              id={fieldName}
              value={value}
              onChange={handleTextAreaChange}
              className={`flex-grow resize-y min-h-[80px] dark:${themeColors.devMutedBg} dark:${themeColors.devBorder} dark:${themeColors.devText} dark:placeholder-gray-400`}
              rows={4}
              disabled={isLoading}
              placeholder={placeholder || `Enter ${label}...`}
            />
          ) : (
            <Input
              id={fieldName}
              type={inputType}
              value={value}
              onChange={handleInputChange}
              className={`flex-grow dark:${themeColors.devMutedBg} dark:${themeColors.devBorder} dark:${themeColors.devText} dark:placeholder-gray-400`}
              disabled={isLoading}
              placeholder={placeholder || `Enter ${label}...`}
            />
          )}
          
          <Button
            size="icon"
            variant="ghost"
            onClick={handleSave}
            disabled={isLoading}
            aria-label={`Save ${label}`}
            className={`dark:${themeColors.devDescText} dark:hover:${themeColors.successText}`}
          >
            <Save className="h-4 w-4" />
          </Button>
          
          <Button
            size="icon"
            variant="ghost"
            onClick={handleCancel}
            disabled={isLoading}
            aria-label={`Cancel editing ${label}`}
            className={`dark:${themeColors.devDescText} dark:hover:${themeColors.errorText}`}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className={`mt-1 flex items-center justify-between group min-h-[40px] py-1 px-2 border border-transparent hover:${themeColors.borderMedium} dark:hover:${themeColors.devBorder} rounded-md transition-colors`}>
          <p className={`${themeColors.textColorPrimary} dark:${themeColors.devText} flex-grow break-words`}>
            {value || (
              <span className={`${themeColors.textColorTertiary} dark:${themeColors.devDisabledText} italic`}>
                Not set
              </span>
            )}
          </p>
          
          <Button
            size="icon"
            variant="ghost"
            onClick={() => setIsEditing(true)}
            className={`opacity-0 group-hover:opacity-100 focus-visible:opacity-100 transition-opacity ml-2 flex-shrink-0 dark:${themeColors.devDescText} dark:hover:${themeColors.devAccent}`}
            aria-label={`Edit ${label}`}
          >
            <Pencil className="h-4 w-4" />
          </Button>
        </div>
      )}
      
      {error && (
        <p className={`mt-1 text-sm ${themeColors.errorText} dark:${themeColors.errorText}`}>
          {error}
        </p>
      )}
    </div>
  );
}
