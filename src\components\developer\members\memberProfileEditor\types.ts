/**
 * MemberProfileEditor 组件相关的类型定义
 * 
 * 本文件包含 MemberProfileEditor 组件及其子组件使用的所有类型定义，
 * 实现类型的集中管理和复用。
 */

// 导入外部类型
import { MemberStatus } from "@prisma/client";
import type {
  Education,
  Award,
  Teaching,
  Project,
  ProjectMember,
  Publication,
  PublicationAuthor,
  AcademicService,
  Presentation,
  SoftwareDataset,
} from "@prisma/client";
import type {
  MemberProfileData,
  PublicationInfo,
} from "@/lib/types";

/**
 * 可编辑的出版物类型
 * 扩展 PublicationInfo，添加特色显示和排序相关字段
 */
export type EditablePublication = PublicationInfo & {
  /** 是否在个人资料中特色显示 */
  isFeatured: boolean;
  /** 在个人资料中的显示顺序 */
  profileDisplayOrder: number | null;
};

/**
 * MemberProfileEditor 组件的 Props 类型
 */
export type MemberProfileEditorProps = {
  /** 成员的初始数据 */
  initialData: MemberProfileData;
};

/**
 * 可编辑文本字段组件的 Props 类型
 */
export type EditableTextFieldProps = {
  /** 字段标签 */
  label: string;
  /** 字段名称，对应 MemberProfileData 的键 */
  fieldName: keyof MemberProfileData;
  /** 初始值，支持字符串、数字或空值 */
  initialValue: string | number | null | undefined;
  /** 成员 ID */
  memberId: string;
  /** 是否为文本域，默认为 false */
  isTextArea?: boolean;
  /** 输入类型，默认为 "text" */
  inputType?: "text" | "email" | "url" | "number";
  /** 占位符文本 */
  placeholder?: string;
};

/**
 * 可编辑的项目信息类型
 * 结合 ProjectMember 和 Project 的信息
 */
export type EditableProjectInfo = ProjectMember & { 
  /** 关联的项目信息 */
  project: Project 
};

/**
 * 区域 ID 类型，用于控制各个区域的展开/折叠状态
 */
export type SectionId =
  | "basicInfo"           // 基本信息
  | "detailedProfile"     // 详细资料
  | "links"               // 链接信息
  | "education"           // 教育经历
  | "awards"              // 获奖情况
  | "featuredPublications" // 特色出版物
  | "projects"            // 项目经历
  | "presentations"       // 演讲报告
  | "softwareDatasets"    // 软件与数据集
  | "patents"             // 专利
  | "academicServices"    // 学术服务
  | "passwordChange"      // 密码修改
  | "usernameChange";     // 用户名修改

/**
 * 头像相关的状态类型
 */
export type AvatarState = {
  /** 当前头像 URL */
  avatarUrl: string;
  /** 选择的头像文件 */
  avatarFile: File | null;
  /** 头像预览 URL */
  avatarPreview: string | null;
  /** 是否正在上传头像 */
  isUploadingAvatar: boolean;
};

/**
 * 模态框状态类型
 */
export type ModalStates = {
  /** 教育经历模态框状态 */
  isEducationModalOpen: boolean;
  /** 获奖情况模态框状态 */
  isAwardModalOpen: boolean;
  /** 教学经历模态框状态 */
  isTeachingModalOpen: boolean;
  /** 项目经历模态框状态 */
  isProjectModalOpen: boolean;
  /** 学术服务模态框状态 */
  isAcademicServiceModalOpen: boolean;
  /** 演讲报告模态框状态 */
  isPresentationModalOpen: boolean;
  /** 软件与数据集模态框状态 */
  isSoftwareDatasetModalOpen: boolean;
};

/**
 * 编辑状态类型
 */
export type EditingStates = {
  /** 正在编辑的教育记录 */
  editingEducationData: Partial<Education> | null;
  /** 正在编辑的获奖记录 */
  editingAward: Partial<Award> | null;
  /** 正在编辑的教学记录 */
  editingTeaching: Partial<Teaching> | null;
  /** 正在编辑的项目数据 */
  editingProjectData: (Project & { memberRole?: string }) | undefined;
  /** 正在编辑的学术服务记录 */
  editingAcademicService: AcademicService | null;
  /** 正在编辑的演讲记录 */
  editingPresentation: Presentation | null;
  /** 正在编辑的软件与数据集记录 */
  editingSoftwareAndDataset: SoftwareDataset | null;
};

/**
 * 数据列表状态类型
 */
export type DataListStates = {
  /** 教育经历列表 */
  educationHistory: Education[];
  /** 获奖列表 */
  awardsList: Award[];
  /** 教学列表 */
  teachingList: Teaching[];
  /** 项目列表 */
  projectsList: EditableProjectInfo[];
  /** 学术服务列表 */
  academicServicesList: AcademicService[];
  /** 演讲列表 */
  presentationList: Presentation[];
  /** 软件与数据集列表 */
  softwareAndDatasetsList: SoftwareDataset[];
  /** 可编辑出版物列表 */
  editablePublications: EditablePublication[];
};

/**
 * 加载状态类型
 */
export type LoadingStates = {
  /** 状态更新加载中 */
  isStatusLoading: boolean;
  /** 可见性更新加载中 */
  isVisibilityLoading: boolean;
  /** 特色出版物保存中 */
  isSavingFeatured: boolean;
};

/**
 * 基本字段状态类型
 */
export type BasicFieldStates = {
  /** 当前状态 */
  currentStatus: MemberStatus | undefined;
  /** 是否公开 */
  isPublic: boolean;
};

/**
 * 区域展开状态类型
 */
export type SectionStates = Record<SectionId, boolean>;
