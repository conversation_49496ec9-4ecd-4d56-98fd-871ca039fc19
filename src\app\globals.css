/* 使用 Tailwind v4 推荐的导入方式 */
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* --- Theme Variables --- */
:root {
  /* 主站主题色 */
  --color-theme-page: #f8f9fa;
  --color-theme-header: #2c3e50;
  --color-theme-header-light: #34495e;
  --color-theme-light: #ecf0f1;
  --color-theme-primary: #2c3e50;
  --color-theme-secondary: #34495e;
  --color-theme-muted: #7f8c8d;
  --color-theme-dark: #1a252f;
  --color-theme: #3498db;
  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
}

/* --- Base Styles --- */
html {
  @apply scroll-smooth;
}

body {
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
  @apply antialiased;
}

/* --- Utility Layers (Tailwind Custom Utilities) --- */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Tremble animation (for last heart) */
  @keyframes tremble {
    0%,
    100% {
      transform: translateX(0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
      transform: translateX(-1px);
    }
    20%,
    40%,
    60%,
    80% {
      transform: translateX(1px);
    }
  }
  .animate-tremble {
    animation: tremble 0.5s ease-in-out infinite;
  }

  /* Flash animation (for disappearing heart) */
  @keyframes flash {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }
  .animate-flash {
    /* Run the flash animation quickly a few times */
    /* Adjust count (5) and duration (0.15s) as needed */
    animation: flash 0.15s step-end 5;
  }
}

/* --- Component Styles --- */

/* Masonry Layout */
.my-masonry-grid {
  display: flex;
  margin-left: -1rem; /* 对应列间距 padding-left */
  width: auto;
}
.my-masonry-grid_column {
  padding-left: 1rem; /* 列间距 */
  background-clip: padding-box;
}
.my-masonry-grid_column > div {
  /* 列内元素 */
  margin-bottom: 1rem; /* 垂直间距 */
}

/* Typewriter Blinking Cursor */
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
.animate-blink {
  animation: blink 1s step-end infinite;
}

/* Serif Font for Titles/Headings */
.font-serif {
  font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
}

/* --- Theme Helper Classes --- */

/* 主站主题辅助类 */
.bg-theme-page {
  background-color: var(--color-theme-page);
}
.bg-theme-header {
  background-color: var(--color-theme-header);
}
.bg-theme-header-light {
  background-color: var(--color-theme-header-light);
}
.text-theme-light {
  color: var(--color-theme-light);
}
.text-theme-primary {
  color: var(--color-theme-primary);
}
.text-theme-secondary {
  color: var(--color-theme-secondary);
}
.text-theme-muted {
  color: var(--color-theme-muted);
}
.border-theme-dark {
  border-color: var(--color-theme-dark);
}
.border-theme-primary {
  border-color: var(--color-theme-primary);
}
.border-theme {
  border-color: var(--color-theme);
}

body.developer-mode-active {
  @apply bg-gray-900; /* 设置开发者模式下的主背景色 */
}

/* 开发者模式下覆盖 Navbar 样式 */
body.developer-mode-active nav[class*="shadow-sm"] {
  @apply bg-gray-800 border-b border-gray-700 shadow-none; /* 暗色背景, 添加边框, 移除阴影 */
}
body.developer-mode-active nav a[class*="text-sm"] {
  @apply text-gray-300 hover:text-green-400 hover:border-green-400; /* 调整导航链接文本和悬停颜色 */
}
body.developer-mode-active nav a[class*="text-xl"] {
  @apply text-gray-100 hover:text-green-400; /* 调整 Logo/标题 链接颜色 */
}

/* 开发者模式下覆盖 Footer 样式 */
body.developer-mode-active footer[class*="pt-8"] {
  @apply bg-gray-900 border-t border-gray-700 text-gray-400; /* 暗色背景, 添加边框, 调整文本颜色 */
}

/* Removed styles specific to Dino page */
/* 
body.dino-active {
  overflow: hidden !important; 
}
main.dino-main-grow {
  flex-grow: 1;
  display: flex; 
  padding-top: 0 !important; 
}
main.dino-main-grow > div {
  flex-grow: 1; 
}
*/

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.208 0.042 265.755);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.929 0.013 255.508);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
