# 项目设计文档

## 1. 主页设计

该部分已经基本完成，不作过多说明

## 2. 开发者页面设计

开发者页面是授权用户管理网站内容的入口。

### 2.1 访问机制

- **触发方式:** Konami Code (上上下下左右左右 B A)。在全局布局 (`src/app/layout.tsx`) 中监听键盘事件序列。
- **导航:** 成功触发 Konami Code 后，使用客户端路由 (`router.push('/developer')`) **导航**到 `/developer` 页面。
- **页面保护:** `/developer` 页面本身需要在**服务器端**进行访问控制。
  - 如果用户未通过认证（无有效 Session/Cookie），则显示登录界面。
  - 如果用户已认证，则显示开发者工具界面。
  - **禁止**未授权用户通过直接输入 URL 访问开发者工具。

### 2.2 认证设计

- **初期方案:** 固定密钥认证。
  - 在 `/developer` 页面未认证时，显示**类 Linux 终端风格**的登录提示符界面。
    - 包含动态显示的 MOTD (系统消息)、`SYSTEM LOCKED` ASCII Art。
    - 提示符样式可为 `Password or Key: ` 或类似。
  - 用户在此界面输入存储在环境变量 (`DEVELOPER_ACCESS_KEY` 或类似名称) 中的预设密钥。
  - 输入时使用 `*` 隐藏，并显示**闪烁的光标**。
  - 后端提供 API (`/api/auth/developer`) 验证密钥（使用 POST 请求）。
  - 认证状态通过**安全的 HttpOnly Cookie 或 Session** 维护，并设置合理的有效期。
- **登录成功 (当前实现流程):**
  1.  **显示进度:** 验证通过后，界面内容切换，显示 `SYSTEM UNLOCKED` ASCII Art 和一个**屏幕居中的文本进度条**，提示 "Unlocking system modules..."。
  2.  **显示欢迎屏幕:** 进度条完成后，界面再次切换，显示 `GOODLAB` ASCII Art、欢迎语和 "Press Enter to continue..." 的提示。
  3.  **等待确认:** 界面停留在欢迎屏幕，等待用户按下 Enter 键。
  4.  **进入工具:** 用户按下 Enter 键后，前端状态更新，渲染实际的开发者工具界面 (`DeveloperDashboard`)。
  - _注意：原设计中的清屏和打字机效果目前未实现。_
  - GOODLAB ASCII Art (效果见实现):
  ```

   ██████╗  ██████╗  ██████╗ ██████╗ ██╗      █████╗ ██████╗
  ██╔════╝ ██╔═══██╗██╔═══██╗██╔══██╗██║     ██╔══██╗██╔══██╗
  ██║  ███╗██║   ██║██║   ██║██║  ██║██║     ███████║██████╔╝
  ██║   ██║██║   ██║██║   ██║██║  ██║██║     ██╔══██║██╔══██╗
  ╚██████╔╝╚██████╔╝╚██████╔╝██████╔╝███████╗██║  ██║██████╔╝
   ╚═════╝  ╚═════╝  ╚═════╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═════╝

  ```
  - 登录界面 ASCII Art:
  ```
  ███████╗██╗   ██╗███████╗████████╗███████╗███╗   ███╗    ██╗      ██████╗  ██████╗██╗  ██╗███████╗██████╗
  ██╔════╝╚██╗ ██╔╝██╔════╝╚══██╔══╝██╔════╝████╗ ████║    ██║     ██╔═══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
  ███████╗ ╚████╔╝ ███████╗   ██║   █████╗  ██╔████╔██║    ██║     ██║   ██║██║     █████╔╝ █████╗  ██║  ██║
  ╚════██║  ╚██╔╝  ╚════██║   ██║   ██╔══╝  ██║╚██╔╝██║    ██║     ██║   ██║██║     ██╔═██╗ ██╔══╝  ██║  ██║
  ███████║   ██║   ███████║   ██║   ███████╗██║ ╚═╝ ██║    ███████╗╚██████╔╝╚██████╗██║  ██╗███████╗██████╔╝
  ╚══════╝   ╚═╝   ╚══════╝   ╚═╝   ╚══════╝╚═╝     ╚═╝    ╚══════╝ ╚═════╝  ╚═════╝╚═╝  ╚═╝╚══════╝╚═════╝

  ```
  - 解锁界面 ASCII Art:
  ```
  ███████╗██╗   ██╗███████╗████████╗███████╗███╗   ███╗    ██╗   ██╗███╗   ██╗██╗      ██████╗  ██████╗██╗  ██╗███████╗██████╗
  ██╔════╝╚██╗ ██╔╝██╔════╝╚══██╔══╝██╔════╝████╗ ████║    ██║   ██║████╗  ██║██║     ██╔═══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
  ███████╗ ╚████╔╝ ███████╗   ██║   █████╗  ██╔████╔██║    ██║   ██║██╔██╗ ██║██║     ██║   ██║██║     █████╔╝ █████╗  ██║  ██║
  ╚════██║  ╚██╔╝  ╚════██║   ██║   ██╔══╝  ██║╚██╔╝██║    ██║   ██║██║╚██╗██║██║     ██║   ██║██║     ██╔═██╗ ██╔══╝  ██║  ██║
  ███████║   ██║   ███████║   ██║   ███████╗██║ ╚═╝ ██║    ╚██████╔╝██║ ╚████║███████╗╚██████╔╝╚██████╗██║  ██╗███████╗██████╔╝
  ╚══════╝   ╚═╝   ╚══════╝   ╚═╝   ╚══════╝╚═╝     ╚═╝     ╚═════╝ ╚═╝  ╚═══╝╚══════╝ ╚═════╝  ╚═════╝╚═╝  ╚═╝╚══════╝╚═════╝

  ```
- **登录失败:**
  - 在终端界面输出**红色**的错误信息，带 `[ERROR]` 标签 (例如 "[ERROR] Authentication failed. Invalid password or key.")。
  - 5 次失败后，显示账户锁定信息和倒计时（锁定逻辑由后端实现）。
- **密钥管理:**
  - 生成/管理：在**安全的、隔离的**内部环境进行。
  - 轮换：固定密钥**必须**定期更新。
- **未来扩展:** 可考虑引入用户名/密码登录或 OAuth。

### 2.3 功能模块规划

开发者页面登录后，应提供以下核心管理功能：

- **新闻管理 (News Management):** 添加、编辑、删除新闻条目。
- **成员管理 (Member Management):** 添加、编辑、删除实验室成员信息。
- **出版物管理 (Publication Management):**
  - 通过 BibTeX 文件批量导入。
  - 手动添加、编辑、删除出版物。
  - (已实现部分) 待处理出版物审批流程。
  - **负责人主页展示:** 在实验室负责人的编辑界面中，提供选项（如滑动菜单、复选框）让老师选择哪些出版物要展示在自己的个人主页上，并允许按 CCF 等级排序。
- **照片墙管理 (Photo Gallery Management):** 上传照片、管理标签、设置特色照片。
- **(待定) 实验室负责人主页编辑 (Lab Chair Profile Editor):**
  - **设计思路:** 提供一个集成式的、所见即所得 (WYSIWYG-like) 的编辑界面，以 `lab_chair/page.tsx` 的展示效果为蓝本。**编辑器本身可不做响应式布局**，优先桌面端体验。
  - **界面:** 复刻老师主页布局，在对应位置添加编辑控件。
  - **编辑方式:**
    - **短文本 (姓名、邮箱等):** 行内编辑控件 (点击图标切换 Input/Select，带 Save/Cancel)。
    - **长文本/格式化文本 (简介、研究陈述等):** 优先使用 **Markdown 编辑器** (`react-md-editor` 备选)，提供基础格式化能力，并明确告知用户支持的语法。若 Markdown 不满足需求，再考虑富文本编辑器 (`TipTap`)。
    - **列表数据 (教育、奖项、服务等):** 使用 **Modal 表单** (`react-hook-form` 处理) 进行添加/编辑。列表项旁提供 Edit/Delete 按钮。使用**开关/复选框**控制 `isFeatured` 状态。利用数据库已有的 `display_order` 字段实现**自定义排序** (可通过 Up/Down 按钮或拖拽库 `@dnd-kit/core` 实现)。
    - **负责人出版物选择/排序:** 在编辑器内提供专门区域或 Modal。后端提供接口获取该负责人所有出版物。使用带复选框的列表供选择。提供排序选项 (CCF/年份)。推荐新建 `FeaturedPublication` 表 (包含 `memberId`, `publicationId`, `display_order`) 来存储选择和排序结果 (方案 B)。
  - **状态管理:** 推荐使用 **Zustand** 管理编辑器整体状态。
- **(已移除)** 运维/工具 (Ops/Tools) - _仅保留 CodeServerManager_
  - `CodeServerManager`: 提供 Code Server 访问代理/转发功能，增强安全性。允许用户配置，提供通用模板。_(具体实现待定)_
  - _KeyGenerator 和 OpsManager 从开发者页面移除，相关功能应在安全的后端或内部工具中实现。_

### 2.4 账户与角色权限 (RBAC)

- **账户标识:** 在 `Member` 数据模型中增加一个**唯一的 `username` 字段**作为登录账户名。
- **角色定义:** 系统定义以下角色，具有不同的权限级别：
  - **`Root`:** 系统最高权限（通常赋予导师），拥有所有管理权限，包括用户和角色管理。
  - **`Admin`:** 网站管理员（通常赋予核心开发者），拥有除管理 Root 账户外的所有管理权限，包括内容管理、设置管理、用户管理（除 Root 外）。
  - **`SeniorMember`:** 资深成员（如博士生、参与实验室管理的学生），拥有广泛的内容管理权限（新闻、出版物、照片），可以管理 `User` 级别的成员信息，可以批准待审核内容。
  - **`Maintainer`:** 板块维护者，拥有特定模块的编辑权限（通过细分权限点实现，见下）。
  - **`User`:** 普通实验室成员，只能编辑**自身**的成员信息，访问基础实验室服务。
  - **`Alumni`:** 已毕业学生，**无任何开发者页面编辑或管理权限**。登录开发者页面后，应明确提示其"已毕业"状态，不显示管理功能。
- **权限点列表 (初步):**
  - `manage_users`: 管理用户账户及角色 (Root/Admin)
  - `manage_roles`: 管理角色定义及权限 (Root)
  - `manage_settings`: 管理网站全局设置 (Root/Admin)
  - `manage_news`: 增删改新闻 (Root/Admin/SeniorMember/Maintainer with 'edit_news')
  - `manage_publications`: 增删改出版物 (Root/Admin/SeniorMember/Maintainer with 'edit_publications')
  - `manage_members`: 管理成员信息 (Root/Admin/SeniorMember 可改 User, User 只能改自己)
  - `manage_photos`: 增删改照片 (Root/Admin/SeniorMember/Maintainer with 'edit_photos')
  - `access_codeserver_basic`: 访问基础 Code Server (User 及以上)
  - `access_codeserver_advanced`: 访问高级 Code Server (SeniorMember 及以上)
  - `approve_content`: 批准待审核内容 (SeniorMember/Admin/Root)
  - _(可根据需要增删权限点)_
- **授权实现:** 主要在后端 API 通过**中间件**实现。
- **前端配合:**
  - 根据从后端获取的用户角色和权限信息，**动态显示/隐藏**开发者工具中的菜单项、功能卡片、按钮或编辑控件。
  - 例如，普通 `User` 登录后只能看到有限的工具（如编辑自己信息），无法看到用户管理、新闻管理等卡片。

### 2.5 数据模型

采用灵活的关系型数据库设计方案：

- **`Member` 表:** 包含成员基本信息，以及新增的 `username` (唯一, non-nullable?) 和可能的 `featuredPublicationConfig: String?` (如果采用方案 A)。
- **`Role` 表:** 存储角色信息 (`id`, `name`, `description`)。
- **`Permission` 表:** 存储细分的权限点 (`id`, `action`, `resource`, `description`)。
- **`MemberRole` 关联表:** 连接 `Member` 和 `Role` (多对多关系)。
- **`RolePermission` 关联表:** 连接 `Role` 和 `Permission` (多对多关系)。
- **`(新增)` `FeaturedPublication` 表:** (如果采用方案 B) 包含 `id`, `memberId`, `publicationId`, `display_order`。

## 3. UI/UX 设计

- **开发者登录界面:**
  - 风格：模拟 Linux 终端提示符，沿用开发者工具配色。
  - 交互：见 2.2 认证设计中的登录成功/失败流程。
  - 防暴力：结合后端 API 实现登录尝试次数限制和锁定（前端通过 ❤️ UI 反馈）。
- **小恐龙彩蛋:**
  - **实现方式:** 创建一个**独立的静态页面** (例如 `/dino`)。
    - **核心:** 使用 **HTML 和 CSS** 模拟 Chrome 断网小恐龙的**视觉外观**，**无需实现完整的游戏逻辑**。
    - **元素:** 使用图片 (SVG 或 PNG) 表示小恐龙、地面、仙人掌等。
    - **动画:** 使用 CSS `background-position` 动画或其他简单方式让地面背景**水平滚动**，模拟前进效果。
    - **静态:** 小恐龙和障碍物本身可以是**静态图片**，固定在页面上。
  - **触发方式:** 监听单独的 Cheat Code (`6031769`)，触发后导航至 `/dino`。
  - **无限生命模式:** 通过 URL 参数 (例如 `/dino?godmode=true`) 激活。
    - 页面加载时检查 URL 参数。
    - 如果 `godmode=true`，可以在页面上显示"Infinite Lives!"的提示，或者**不渲染障碍物图片**。
- **编辑器界面:**
  - 遵循"复用展示页面，替换为编辑控件"的原则。
  - 优先保证桌面端易用性，复杂编辑部分可不做响应式。

## 4. 技术选型

- **核心框架:** Next.js (App Router)
- **UI 库:** Tailwind CSS, shadcn/ui (推荐引入，提供基础组件), lucide-react (图标)
- **动画:** Framer Motion
- **状态管理:** Zustand (全局状态), React Context (少量共享状态), `useState`/`useReducer` (局部状态)
- **表单处理:** `react-hook-form`
- **Markdown 编辑器:** `react-md-editor` (备选)
- **富文本编辑器:** `TipTap` (若 Markdown 不足)
- **拖拽排序:** `@dnd-kit/core` (可选)
- **数据库 ORM:** Prisma
- **认证:** NextAuth.js (推荐引入，简化 Session/Cookie 管理，支持未来扩展多种认证方式) 或 手动实现基于 Cookie/Session 的认证。
- **API 请求:** Axios 或 `fetch` (服务器组件)
- **数据获取/缓存 (客户端):** React Query (TanStack Query) (推荐引入)
- **打字机组件 (`Typewriter.tsx`):** 需要升级以支持多行文本（换行符）和结束时光标控制。

## 5. 项目进度与状态

- **第一阶段目标:** 实现开发者页面登录 (终端风格, 含 ASCII Art 和 MOTD) 与 RBAC 权限验证 (后端 API + 前端 UI 控制)。
- **第二阶段目标:** 实现 Lab Chair Profile Editor。
- ...

## 6. 后续改进项 (Deferred Features)

以下功能和效果被暂时搁置，可在核心功能稳定后考虑实现：

- **登录界面动态 ASCII Art:** 在用户成功输入密码后，通过打字机或其他动画效果将登录界面的 "SYSTEM LOCKED" ASCII Art 动态替换为 "SYSTEM UNLOCKED"。 (当前是直接替换)
- **登录进度条:** 在登录认证过程中（`isLoading` 状态），在登录界面显示模拟的文本进度条。 (当前在成功后显示)
- **字符雨背景效果:** 为开发者页面添加类似 Matrix 的动态字符雨背景动画。
- **Typewriter 组件升级:** 增强 `Typewriter.tsx` 组件，以支持更复杂的文本操作（如动态替换部分文本）以及更好的换行和光标控制。
- **心形颤抖动画:** 在登录失败且仅剩一次尝试机会时，为最后的心形图标添加颤抖动画。 (已实现)

## 7. 登录失败后的处理逻辑

- 登录失败后，用户将看到红色错误信息，并显示剩余尝试次数。
- 每次失败，❤️ 数量减少一个。
- 当仅剩 1 颗 ❤️ 时，该 ❤️ 显示**颤抖动画**。
- 5 次失败后，显示账户锁定信息和倒计时（锁定逻辑由后端实现）。
