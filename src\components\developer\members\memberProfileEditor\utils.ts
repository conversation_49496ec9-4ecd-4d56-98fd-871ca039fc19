/**
 * MemberProfileEditor 组件相关的工具函数
 * 
 * 本文件包含各种纯函数和业务逻辑处理函数，
 * 实现业务逻辑与 UI 组件的分离。
 */

import { MemberStatus } from "@prisma/client";
import type { EditablePublication, SectionStates } from "./types";
import type { PublicationInfo } from "@/lib/types";

/**
 * 格式化成员状态标签
 * 将 MemberStatus 枚举值转换为用户友好的显示文本
 * 
 * @param status - 成员状态枚举值
 * @returns 格式化后的状态标签字符串
 */
export function formatStatusLabel(status: MemberStatus): string {
  switch (status) {
    case MemberStatus.PROFESSOR:
      return "Professor";
    case MemberStatus.POSTDOC:
      return "Postdoc";
    case MemberStatus.PHD_STUDENT:
      return "PhD Student";
    case MemberStatus.MASTER_STUDENT:
      return "Master Student";
    case MemberStatus.UNDERGRADUATE:
      return "Undergraduate";
    case MemberStatus.RESEARCH_STAFF:
      return "Research Staff";
    case MemberStatus.VISITING_SCHOLAR:
      return "Visiting Scholar";
    case MemberStatus.ALUMNI:
      return "Alumni";
    case MemberStatus.OTHER:
      return "Other";
    default:
      // 处理未知状态 - 记录警告并返回原始值作为后备
      console.warn(`Unknown member status encountered: ${status}`);
      return status;
  }
}

/**
 * 验证头像文件类型和大小
 * 检查上传的文件是否符合要求
 * 
 * @param file - 要验证的文件对象
 * @returns 验证结果对象，包含是否有效和错误信息
 */
export function validateAvatarFile(file: File): { 
  isValid: boolean; 
  error?: string 
} {
  // 允许的文件类型
  const allowedTypes = [
    "image/jpeg",
    "image/png", 
    "image/gif",
    "image/webp",
    "image/jpg",
  ];

  // 检查文件类型
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: "Only JPG, PNG, GIF, and WEBP images are supported."
    };
  }

  // 检查文件大小（5MB 限制）
  const maxSize = 5 * 1024 * 1024; // 5MB in bytes
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: "Image size must not exceed 5MB."
    };
  }

  return { isValid: true };
}

/**
 * 创建头像预览 URL
 * 为选择的文件创建预览 URL
 * 
 * @param file - 头像文件
 * @returns 预览 URL 字符串
 */
export function createAvatarPreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * 验证成员状态值是否有效
 * 检查给定的字符串是否为有效的 MemberStatus 枚举值
 * 
 * @param statusValue - 要验证的状态值字符串
 * @returns 是否为有效状态值
 */
export function isValidMemberStatus(statusValue: string): boolean {
  return Object.values(MemberStatus).includes(statusValue as MemberStatus);
}

/**
 * 初始化可编辑出版物列表
 * 由于 EditablePublication 现在就是 PublicationInfo，直接返回原数组
 * 确保每个出版物都有必要的字段
 *
 * @param publications - 原始出版物信息数组
 * @returns 可编辑出版物数组
 */
export function initializeEditablePublications(
  publications: PublicationInfo[]
): EditablePublication[] {
  // 由于 EditablePublication 就是 PublicationInfo，直接返回
  // PublicationInfo 已经包含了 isFeatured 和 profileDisplayOrder 字段
  return publications;
}

/**
 * 处理数字输入值
 * 将字符串输入转换为数字或 null
 * 
 * @param value - 输入的字符串值
 * @param inputType - 输入类型
 * @returns 处理后的值（字符串或 null）
 */
export function processInputValue(
  value: string, 
  inputType: string
): string | null {
  // 处理数字输入的空字符串情况
  if (inputType === "number" && value.trim() === "") {
    return null;
  }
  return value;
}

/**
 * 验证数字输入
 * 检查数字输入是否有效
 * 
 * @param value - 要验证的值
 * @param inputType - 输入类型
 * @returns 验证结果对象
 */
export function validateNumberInput(
  value: string | null, 
  inputType: string
): { isValid: boolean; error?: string } {
  if (inputType === "number" && value !== null && isNaN(Number(value))) {
    return {
      isValid: false,
      error: "Please enter a valid number."
    };
  }
  return { isValid: true };
}

/**
 * 格式化显示值
 * 将值格式化为适合显示的字符串
 * 
 * @param value - 要格式化的值
 * @returns 格式化后的显示字符串
 */
export function formatDisplayValue(value: string | number | null | undefined): string {
  if (value === null || value === undefined || value === "") {
    return "";
  }
  return value.toString();
}

/**
 * 创建特色出版物更新数据
 * 根据可编辑出版物列表创建更新数据
 * 
 * @param editablePublications - 可编辑出版物列表
 * @returns 特色出版物更新数据数组
 */
export function createFeaturedPublicationsUpdateData(
  editablePublications: EditablePublication[]
) {
  return editablePublications.map((pub, index) => ({
    publicationId: pub.id,
    isFeatured: pub.isFeatured,
    order: pub.profileDisplayOrder ?? index,
  }));
}

/**
 * 查找拖拽项目的索引
 * 在数组中查找指定 ID 的项目索引
 * 
 * @param items - 项目数组
 * @param activeId - 活动项目的 ID
 * @returns 项目索引，未找到返回 -1
 */
export function findDragItemIndex(
  items: EditablePublication[], 
  activeId: string | number
): number {
  return items.findIndex((item) => item.id.toString() === activeId.toString());
}

/**
 * 验证拖拽索引
 * 检查拖拽操作的索引是否有效
 * 
 * @param oldIndex - 原始索引
 * @param newIndex - 新索引
 * @returns 索引是否有效
 */
export function validateDragIndices(oldIndex: number, newIndex: number): boolean {
  return oldIndex !== -1 && newIndex !== -1;
}

/**
 * 解析 Zod 验证错误
 * 将 Zod 错误信息格式化为用户友好的文本
 * 
 * @param errorMessage - 错误信息字符串
 * @returns 格式化后的错误信息
 */
export function parseZodError(errorMessage: string): string {
  try {
    const fieldErrors = JSON.parse(errorMessage);
    return `Validation Error: ${Object.entries(fieldErrors)
      .map(([field, errors]) => 
        `${field}: ${(errors as string[]).join(", ")}`
      )
      .join("; ")}`;
  } catch (e) {
    return `Error: ${errorMessage || "An unknown error occurred"}`;
  }
}

/**
 * 生成默认的区域展开状态
 * 创建所有区域默认折叠的状态对象
 *
 * @returns 区域展开状态对象
 */
export function getDefaultSectionStates(): SectionStates {
  return {
    basicInfo: false,
    detailedProfile: false,
    links: false,
    education: false,
    awards: false,
    featuredPublications: false,
    projects: false,
    presentations: false,
    softwareDatasets: false,
    patents: false,
    academicServices: false,
    passwordChange: false,
    usernameChange: false,
  };
}

/**
 * 清理头像预览 URL
 * 释放通过 URL.createObjectURL 创建的对象 URL
 *
 * @param url - 要清理的 URL
 */
export function cleanupAvatarPreview(url: string | null): void {
  if (url) {
    URL.revokeObjectURL(url);
  }
}

/**
 * 验证邮箱格式
 * 检查邮箱地址是否符合基本格式要求
 *
 * @param email - 邮箱地址
 * @returns 是否为有效邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证 URL 格式
 * 检查 URL 是否符合基本格式要求
 *
 * @param url - URL 字符串
 * @returns 是否为有效 URL 格式
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 格式化年份显示
 * 将年份数字格式化为字符串，处理空值情况
 *
 * @param year - 年份数字
 * @returns 格式化后的年份字符串
 */
export function formatYear(year: number | null | undefined): string {
  if (!year) return "";
  return year.toString();
}

/**
 * 安全地解析 JSON 字符串
 * 尝试解析 JSON，失败时返回默认值
 *
 * @param jsonString - JSON 字符串
 * @param defaultValue - 解析失败时的默认值
 * @returns 解析结果或默认值
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString) as T;
  } catch {
    return defaultValue;
  }
}
