/**
 * MemberProfileEditor 模块统一导出文件
 * 
 * 本文件提供模块的统一导出接口，保持向后兼容性，
 * 方便其他组件导入和使用模块化后的功能。
 */

// 类型定义导出
export type {
  EditablePublication,
  MemberProfileEditorProps,
  EditableTextFieldProps,
  EditableProjectInfo,
  SectionId,
  AvatarState,
  ModalStates,
  EditingStates,
  DataListStates,
  LoadingStates,
  BasicFieldStates,
  SectionStates,
} from "./types";

// 工具函数导出
export {
  formatStatusLabel,
  validateAvatarFile,
  createAvatarPreview,
  isValidMemberStatus,
  initializeEditablePublications,
  processInputValue,
  validateNumberInput,
  formatDisplayValue,
  createFeaturedPublicationsUpdateData,
  findDragItemIndex,
  validateDragIndices,
  parseZodError,
  getDefaultSectionStates,
} from "./utils";

// 查询函数导出
export {
  updateMemberFieldQuery,
  updateMemberStatusQuery,
  updateMemberVisibilityQuery,
  uploadAvatarQuery,
  updateFeaturedPublicationsQuery,
  educationQueries,
  awardQueries,
  teachingQueries,
  projectQueries,
  academicServiceQueries,
  presentationQueries,
  softwareDatasetQueries,
} from "./queries";

// 事件处理函数导出
export {
  createHandlers,
  createTeachingHandlers,
  createProjectHandlers,
  createAcademicServiceHandlers,
  createPresentationHandlers,
  createSoftwareDatasetHandlers,
} from "./handlers";

// 组件导出
export { EditableTextField } from "./components/EditableTextField";

// 重新导出外部依赖的类型，方便使用
export type { EducationFormData } from "@/app/actions/educationActions";
export type { AwardFormData } from "@/app/actions/awardActions";
export type { TeachingFormData } from "@/app/actions/teachingActions";
export type { ProjectFormData } from "@/app/actions/projectActions";
export type { AcademicServiceFormData } from "@/app/actions/academicServiceActions";
export type { PresentationFormData } from "@/app/actions/presentationActions";
export type { SoftwareDatasetFormData } from "@/app/actions/softwareDatasetActions";

// 重新导出 Prisma 类型
export type {
  Education,
  Award,
  Teaching,
  Project,
  ProjectMember,
  AcademicService,
  Presentation,
  SoftwareDataset,
} from "@prisma/client";

// 重新导出应用类型
export type {
  MemberProfileData,
  PublicationInfo,
} from "@/lib/types";
