/**
 * 成员资料编辑器组件 - 重构版本
 * 
 * 本组件经过完整的模块化重构，将业务逻辑、数据处理、工具函数等分离到独立模块中，
 * 实现了信息局部化原则和关注点分离。
 */

"use client";

// React and Hooks
import React, { useState, useMemo } from "react";

// Framer Motion for Animations
import { motion, AnimatePresence } from "framer-motion";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ChevronDown,
  ChevronUp,
  Info,
  KeyRound,
  UserCog,
  ClipboardList,
  Link,
  GraduationCap,
  Award as AwardIcon,
  Star,
  Briefcase,
  Presentation as PresentationIcon,
  Database,
  ScrollText,
  Users,
} from "lucide-react";

// Dnd-Kit for Drag and Drop
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

// 主题颜色导入
import { themeColors } from "@/styles/theme";

// 模块化导入
import {
  // 类型
  type MemberProfileEditorProps,
  type EditablePublication,
  type EditableProjectInfo,
  type SectionId,
  // 工具函数
  formatStatusLabel,
  initializeEditablePublications,
  getDefaultSectionStates,
  // 处理函数
  createHandlers,
  createTeachingHandlers,
  createProjectHandlers,
  createAcademicServiceHandlers,
  createPresentationHandlers,
  createSoftwareDatasetHandlers,
  // 组件
  EditableTextField,
  // 类型重新导出
  type Education,
  type Award,
  type Teaching,
  type Project,
  type AcademicService,
  type Presentation,
  type SoftwareDataset,
} from "./memberProfileEditor/index";

// Custom Modal Components
import { EducationFormModal } from "./EducationFormModal";
import { AwardFormModal } from "./AwardFormModal";
import { TeachingFormModal } from "./TeachingFormModal";
import { ProjectFormModal } from "./ProjectFormModal";
import { AcademicServiceFormModal } from "./AcademicServiceFormModal";
import { PresentationFormModal } from "./PresentationFormModal";
import { SoftwareDatasetFormModal } from "./SoftwareDatasetFormModal";
import { PasswordChangeForm } from "./PasswordChangeForm";
import { UsernameChangeForm } from "./UsernameChangeForm";
import { MemberProfileImage } from "@/components/members/MemberProfileImage";

/**
 * 可排序的出版物项目组件
 */
function SortablePublicationItem({ 
  publication, 
  onToggleFeatured 
}: { 
  publication: EditablePublication;
  onToggleFeatured: (id: number) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: publication.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`p-3 border rounded-lg ${themeColors.borderMedium} dark:${themeColors.devBorder} ${themeColors.backgroundWhite} dark:${themeColors.devMutedBg} cursor-move`}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h4 className={`font-medium ${themeColors.textColorPrimary} dark:${themeColors.devText}`}>
            {publication.title}
          </h4>
          <p className={`text-sm ${themeColors.textColorSecondary} dark:${themeColors.devDescText}`}>
            {publication.venue} ({publication.year})
          </p>
        </div>
        <Checkbox
          checked={publication.isFeatured}
          onCheckedChange={() => onToggleFeatured(publication.id)}
          className="ml-4"
        />
      </div>
    </div>
  );
}

/**
 * 主编辑器组件
 */
export default function MemberProfileEditor({
  initialData,
}: MemberProfileEditorProps) {
  // --- 基本状态管理 ---
  const [currentStatus, setCurrentStatus] = useState(initialData.status);
  const [isStatusLoading, setIsStatusLoading] = useState(false);
  const [isPublic, setIsPublic] = useState(initialData.is_profile_public ?? true);
  const [isVisibilityLoading, setIsVisibilityLoading] = useState(false);

  // --- 头像状态 ---
  const [avatarUrl, setAvatarUrl] = useState(initialData.avatar_url || "/avatars/placeholder.png");
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);

  // --- 模态框状态 ---
  const [isEducationModalOpen, setIsEducationModalOpen] = useState(false);
  const [isAwardModalOpen, setIsAwardModalOpen] = useState(false);
  const [isTeachingModalOpen, setIsTeachingModalOpen] = useState(false);
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const [isAcademicServiceModalOpen, setIsAcademicServiceModalOpen] = useState(false);
  const [isPresentationModalOpen, setIsPresentationModalOpen] = useState(false);
  const [isSoftwareDatasetModalOpen, setIsSoftwareDatasetModalOpen] = useState(false);

  // --- 编辑状态 ---
  const [editingEducationData, setEditingEducationData] = useState<Partial<Education> | null>(null);
  const [editingAward, setEditingAward] = useState<Partial<Award> | null>(null);
  const [editingTeaching, setEditingTeaching] = useState<Partial<Teaching> | null>(null);
  const [editingProjectData, setEditingProjectData] = useState<(Project & { memberRole?: string }) | undefined>(undefined);
  const [editingAcademicService, setEditingAcademicService] = useState<AcademicService | null>(null);
  const [editingPresentation, setEditingPresentation] = useState<Presentation | null>(null);
  const [editingSoftwareAndDataset, setEditingSoftwareAndDataset] = useState<SoftwareDataset | null>(null);

  // --- 数据列表状态 ---
  const [educationHistory, setEducationHistory] = useState<Education[]>(initialData.educationHistory || []);
  const [awardsList, setAwardsList] = useState<Award[]>(initialData.awards || []);
  const [teachingList, setTeachingList] = useState<Teaching[]>(initialData.teachingRoles || []);
  const [projectsList, setProjectsList] = useState<EditableProjectInfo[]>(initialData.projects || []);
  const [academicServicesList, setAcademicServicesList] = useState<AcademicService[]>(initialData.academicServices || []);
  const [presentationList, setPresentationList] = useState<Presentation[]>(initialData.presentations || []);
  const [softwareAndDatasetsList, setSoftwareAndDatasetsList] = useState<SoftwareDataset[]>(initialData.softwareAndDatasets || []);

  // --- 出版物状态 ---
  const [editablePublications, setEditablePublications] = useState<EditablePublication[]>(
    () => initializeEditablePublications(initialData.publications)
  );
  const [isSavingFeatured, setIsSavingFeatured] = useState(false);

  // --- 区域展开状态 ---
  const [openSections, setOpenSections] = useState(getDefaultSectionStates());

  // --- 拖拽传感器设置 ---
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // --- 创建处理函数 ---
  const handlers = useMemo(() => createHandlers(
    initialData,
    {
      avatar: { avatarUrl, avatarFile, avatarPreview, isUploadingAvatar },
      modal: {
        isEducationModalOpen,
        isAwardModalOpen,
        isTeachingModalOpen,
        isProjectModalOpen,
        isAcademicServiceModalOpen,
        isPresentationModalOpen,
        isSoftwareDatasetModalOpen,
      },
      editing: {
        editingEducationData,
        editingAward,
        editingTeaching,
        editingProjectData,
        editingAcademicService,
        editingPresentation,
        editingSoftwareAndDataset,
      },
      dataList: {
        educationHistory,
        awardsList,
        teachingList,
        projectsList,
        academicServicesList,
        presentationList,
        softwareAndDatasetsList,
        editablePublications,
      },
      loading: {
        isStatusLoading,
        isVisibilityLoading,
        isSavingFeatured,
      },
      basicField: {
        currentStatus,
        isPublic,
      },
      section: openSections,
    },
    {
      setAvatarUrl,
      setAvatarFile,
      setAvatarPreview,
      setIsUploadingAvatar,
      setCurrentStatus,
      setIsStatusLoading,
      setIsPublic,
      setIsVisibilityLoading,
      setOpenSections,
      setEducationHistory,
      setAwardsList,
      setTeachingList,
      setProjectsList,
      setAcademicServicesList,
      setPresentationList,
      setSoftwareAndDatasetsList,
      setEditablePublications,
      setIsSavingFeatured,
      setIsEducationModalOpen,
      setEditingEducationData,
      setIsAwardModalOpen,
      setEditingAward,
      setIsTeachingModalOpen,
      setEditingTeaching,
      setIsProjectModalOpen,
      setEditingProjectData,
      setIsAcademicServiceModalOpen,
      setEditingAcademicService,
      setIsPresentationModalOpen,
      setEditingPresentation,
      setIsSoftwareDatasetModalOpen,
      setEditingSoftwareAndDataset,
    }
  ), [
    initialData,
    avatarUrl, avatarFile, avatarPreview, isUploadingAvatar,
    currentStatus, isStatusLoading, isPublic, isVisibilityLoading,
    openSections, editablePublications, isSavingFeatured,
    // ... 其他依赖项
  ]);

  // 创建其他处理函数
  const teachingHandlers = useMemo(() => createTeachingHandlers(
    initialData,
    { setIsTeachingModalOpen, setEditingTeaching, setTeachingList }
  ), [initialData]);

  const projectHandlers = useMemo(() => createProjectHandlers(
    initialData,
    { setIsProjectModalOpen, setEditingProjectData, setProjectsList }
  ), [initialData]);

  const academicServiceHandlers = useMemo(() => createAcademicServiceHandlers(
    initialData,
    { setIsAcademicServiceModalOpen, setEditingAcademicService, setAcademicServicesList }
  ), [initialData]);

  const presentationHandlers = useMemo(() => createPresentationHandlers(
    initialData,
    { setIsPresentationModalOpen, setEditingPresentation, setPresentationList }
  ), [initialData]);

  const softwareDatasetHandlers = useMemo(() => createSoftwareDatasetHandlers(
    initialData,
    { setIsSoftwareDatasetModalOpen, setEditingSoftwareAndDataset, setSoftwareAndDatasetsList }
  ), [initialData]);

  return (
    <div className="space-y-6 pb-10">
      {/* 头像上传区域 */}
      <Card className={`overflow-hidden border-green-500/50 dark:border-green-400/40 mb-6 rounded-xl`}>
        <CardHeader className="flex flex-row items-center gap-6 px-3 py-4">
          <div className="flex flex-col items-center justify-center">
            <MemberProfileImage
              src={avatarPreview || avatarUrl}
              alt={initialData.name_en + " avatar"}
              width={96}
              height={96}
              className="rounded-full border-2 border-green-400 shadow-md mb-2"
            />
            <span className="text-xs text-green-400">Avatar Preview</span>
          </div>
          <div className="flex flex-col gap-2 flex-1">
            <input
              type="file"
              accept="image/*"
              id="avatar-upload-input"
              className="hidden"
              onChange={handlers.handleAvatarChange}
              disabled={isUploadingAvatar}
            />
            <label htmlFor="avatar-upload-input">
              <Button
                asChild
                variant="outline"
                size="sm"
                disabled={isUploadingAvatar}
                className="border-green-500 text-green-700 dark:border-green-400 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900"
              >
                <span>Select New Avatar</span>
              </Button>
            </label>
            {avatarPreview && (
              <Button
                variant="default"
                size="sm"
                onClick={handlers.handleUploadAvatar}
                disabled={isUploadingAvatar}
                className="bg-green-600 hover:bg-green-700 text-white dark:bg-green-700 dark:hover:bg-green-800"
              >
                {isUploadingAvatar ? "Uploading..." : "Upload and Save Avatar"}
              </Button>
            )}
            <span className="text-xs text-green-500">
              Supported: JPG/PNG/GIF/WEBP, Max 5MB
            </span>
          </div>
        </CardHeader>
      </Card>

      {/* 基本信息区域 */}
      <Card className={`overflow-hidden border-green-500/50 dark:border-green-400/40`}>
        <CardHeader
          className="flex flex-row items-center justify-between cursor-pointer px-3 py-0"
          onClick={() => handlers.toggleSection("basicInfo")}
        >
          <CardTitle className={`text-lg flex items-center gap-2 text-green-700 dark:text-green-400`}>
            <Info className="h-5 w-5" />
            Basic Information
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            aria-label={
              openSections.basicInfo
                ? "Collapse Basic Information"
                : "Expand Basic Information"
            }
            className="self-center text-green-500 dark:text-green-400"
          >
            <motion.div animate={{ rotate: openSections.basicInfo ? 180 : 0 }}>
              <ChevronDown className="h-4 w-4" />
            </motion.div>
          </Button>
        </CardHeader>

        <AnimatePresence>
          {openSections.basicInfo && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <CardContent className="px-3 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 状态选择 */}
                  <div>
                    <label className={`text-sm font-medium ${themeColors.textColorPrimary} dark:${themeColors.devMutedText} block mb-1`}>
                      Status
                    </label>
                    <Select
                      value={currentStatus || ""}
                      onValueChange={handlers.handleStatusChange}
                      disabled={isStatusLoading}
                    >
                      <SelectTrigger className={`dark:${themeColors.devMutedBg} dark:${themeColors.devBorder} dark:${themeColors.devText}`}>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(MemberStatus).map((status) => (
                          <SelectItem key={status} value={status}>
                            {formatStatusLabel(status)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 可见性开关 */}
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="profile-visibility"
                      checked={isPublic}
                      onCheckedChange={handlers.handleVisibilityChange}
                      disabled={isVisibilityLoading}
                    />
                    <label
                      htmlFor="profile-visibility"
                      className={`text-sm font-medium ${themeColors.textColorPrimary} dark:${themeColors.devMutedText}`}
                    >
                      Public Profile
                    </label>
                  </div>
                </div>

                {/* 可编辑字段 */}
                <div className="mt-4 space-y-4">
                  <EditableTextField
                    label="Name (English)"
                    fieldName="name_en"
                    initialValue={initialData.name_en}
                    memberId={initialData.id}
                  />
                  <EditableTextField
                    label="Name (Chinese)"
                    fieldName="name_zh"
                    initialValue={initialData.name_zh}
                    memberId={initialData.id}
                  />
                  <EditableTextField
                    label="Email"
                    fieldName="email"
                    initialValue={initialData.email}
                    memberId={initialData.id}
                    inputType="email"
                  />
                  <EditableTextField
                    label="Bio"
                    fieldName="bio"
                    initialValue={initialData.bio}
                    memberId={initialData.id}
                    isTextArea={true}
                  />
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* 特色出版物区域 */}
      <Card className={`overflow-hidden border-green-500/50 dark:border-green-400/40`}>
        <CardHeader
          className="flex flex-row items-center justify-between cursor-pointer px-3 py-0"
          onClick={() => handlers.toggleSection("featuredPublications")}
        >
          <CardTitle className={`text-lg flex items-center gap-2 text-green-700 dark:text-green-400`}>
            <Star className="h-5 w-5" />
            Featured Publications
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className="self-center text-green-500 dark:text-green-400"
          >
            <motion.div animate={{ rotate: openSections.featuredPublications ? 180 : 0 }}>
              <ChevronDown className="h-4 w-4" />
            </motion.div>
          </Button>
        </CardHeader>

        <AnimatePresence>
          {openSections.featuredPublications && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <CardContent className="px-3 pb-4">
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handlers.handleDragEnd}
                >
                  <SortableContext
                    items={editablePublications.map(p => p.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-2">
                      {editablePublications.map((publication) => (
                        <SortablePublicationItem
                          key={publication.id}
                          publication={publication}
                          onToggleFeatured={handlers.handleToggleFeatured}
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>

                <div className="mt-4 flex justify-end">
                  <Button
                    onClick={handlers.handleSaveFeaturedPublications}
                    disabled={isSavingFeatured}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    {isSavingFeatured ? "Saving..." : "Save Featured Publications"}
                  </Button>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* 模态框组件 */}
      <EducationFormModal
        isOpen={isEducationModalOpen}
        onClose={handlers.education.close}
        onSubmit={handlers.education.submit}
        editingData={editingEducationData}
      />

      <AwardFormModal
        isOpen={isAwardModalOpen}
        onClose={handlers.award.close}
        onSubmit={handlers.award.submit}
        editingData={editingAward}
      />

      <TeachingFormModal
        isOpen={isTeachingModalOpen}
        onClose={teachingHandlers.close}
        onSubmit={teachingHandlers.submit}
        editingData={editingTeaching}
      />

      <ProjectFormModal
        isOpen={isProjectModalOpen}
        onClose={projectHandlers.close}
        onSubmit={projectHandlers.submit}
        editingData={editingProjectData}
      />

      <AcademicServiceFormModal
        isOpen={isAcademicServiceModalOpen}
        onClose={academicServiceHandlers.close}
        onSubmit={academicServiceHandlers.submit}
        editingData={editingAcademicService}
      />

      <PresentationFormModal
        isOpen={isPresentationModalOpen}
        onClose={presentationHandlers.close}
        onSubmit={presentationHandlers.submit}
        editingData={editingPresentation}
      />

      <SoftwareDatasetFormModal
        isOpen={isSoftwareDatasetModalOpen}
        onClose={softwareDatasetHandlers.close}
        onSubmit={(data) => softwareDatasetHandlers.submit(data, editingSoftwareAndDataset)}
        editingData={editingSoftwareAndDataset}
      />
    </div>
  );
}
