/**
 * 模块化重构测试文件
 * 
 * 用于验证所有模块的导入和导出是否正常工作
 */

// 测试所有模块的导入
import {
  // 类型
  type MemberProfileEditorProps,
  type EditablePublication,
  type EditableProjectInfo,
  type SectionId,
  type AvatarState,
  type ModalStates,
  type EditingStates,
  type DataListStates,
  type LoadingStates,
  type BasicFieldStates,
  type SectionStates,
  
  // 工具函数
  formatStatusLabel,
  validateAvatarFile,
  createAvatarPreview,
  isValidMemberStatus,
  initializeEditablePublications,
  processInputValue,
  validateNumberInput,
  formatDisplayValue,
  createFeaturedPublicationsUpdateData,
  findDragItemIndex,
  validateDragIndices,
  parseZodError,
  getDefaultSectionStates,
  
  // 查询函数
  updateMemberFieldQuery,
  updateMemberStatusQuery,
  updateMemberVisibilityQuery,
  uploadAvatarQuery,
  updateFeaturedPublicationsQuery,
  educationQueries,
  awardQueries,
  teachingQueries,
  projectQueries,
  academicServiceQueries,
  presentationQueries,
  softwareDatasetQueries,
  
  // 事件处理函数
  createHandlers,
  createTeachingHandlers,
  createProjectHandlers,
  createAcademicServiceHandlers,
  createPresentationHandlers,
  createSoftwareDatasetHandlers,
  
  // 组件
  EditableTextField,
  
  // 重新导出的类型
  type Education,
  type Award,
  type Teaching,
  type Project,
  type AcademicService,
  type Presentation,
  type SoftwareDataset,
  type EducationFormData,
  type AwardFormData,
  type TeachingFormData,
  type ProjectFormData,
  type AcademicServiceFormData,
  type PresentationFormData,
  type SoftwareDatasetFormData,
  type MemberProfileData,
  type PublicationInfo,
} from "./index";

import { MemberStatus } from "@prisma/client";

/**
 * 测试工具函数
 */
export function testUtilityFunctions() {
  console.log("Testing utility functions...");
  
  // 测试状态格式化
  const formattedStatus = formatStatusLabel(MemberStatus.PROFESSOR);
  console.log("Formatted status:", formattedStatus);
  
  // 测试状态验证
  const isValid = isValidMemberStatus("PROFESSOR");
  console.log("Is valid status:", isValid);
  
  // 测试默认区域状态
  const defaultSections = getDefaultSectionStates();
  console.log("Default sections:", defaultSections);
  
  // 测试输入值处理
  const processedValue = processInputValue("123", "number");
  console.log("Processed value:", processedValue);
  
  // 测试数字验证
  const validation = validateNumberInput("123", "number");
  console.log("Number validation:", validation);
  
  // 测试显示值格式化
  const displayValue = formatDisplayValue("test value");
  console.log("Display value:", displayValue);
  
  console.log("All utility functions tested successfully!");
}

/**
 * 测试查询函数结构
 */
export function testQueryFunctions() {
  console.log("Testing query functions structure...");
  
  // 验证查询函数对象存在
  console.log("Education queries:", typeof educationQueries);
  console.log("Award queries:", typeof awardQueries);
  console.log("Teaching queries:", typeof teachingQueries);
  console.log("Project queries:", typeof projectQueries);
  console.log("Academic service queries:", typeof academicServiceQueries);
  console.log("Presentation queries:", typeof presentationQueries);
  console.log("Software dataset queries:", typeof softwareDatasetQueries);
  
  console.log("All query functions structure verified!");
}

/**
 * 测试处理函数工厂
 */
export function testHandlerFactories() {
  console.log("Testing handler factories...");
  
  // 验证处理函数工厂存在
  console.log("createHandlers:", typeof createHandlers);
  console.log("createTeachingHandlers:", typeof createTeachingHandlers);
  console.log("createProjectHandlers:", typeof createProjectHandlers);
  console.log("createAcademicServiceHandlers:", typeof createAcademicServiceHandlers);
  console.log("createPresentationHandlers:", typeof createPresentationHandlers);
  console.log("createSoftwareDatasetHandlers:", typeof createSoftwareDatasetHandlers);
  
  console.log("All handler factories verified!");
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log("=== 开始模块化重构测试 ===");
  
  try {
    testUtilityFunctions();
    testQueryFunctions();
    testHandlerFactories();
    
    console.log("=== 所有测试通过！模块化重构成功！ ===");
    return true;
  } catch (error) {
    console.error("=== 测试失败 ===", error);
    return false;
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === "undefined") {
  // Node.js 环境
  runAllTests();
}
