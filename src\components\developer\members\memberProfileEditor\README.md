# MemberProfileEditor 模块化重构文档

## 📋 概述

本目录包含 `MemberProfileEditor` 组件的完整模块化重构版本。重构遵循信息局部化原则和关注点分离，将原本 3000+ 行的单一文件拆分为多个专职模块。

## 🏗️ 模块结构

```
memberProfileEditor/
├── types.ts                    # 类型定义模块
├── utils.ts                    # 工具函数和业务逻辑模块
├── queries.ts                  # 数据查询和 API 调用模块
├── handlers.ts                 # 事件处理函数模块
├── components/
│   └── EditableTextField.tsx   # 可编辑文本字段组件
├── index.ts                    # 统一导出接口
├── test.ts                     # 模块测试文件
└── README.md                   # 本文档
```

## 📦 模块详细说明

### 1. types.ts - 类型定义模块

**职责**: 集中管理所有类型定义，实现类型的复用和维护性

**主要类型**:
- `MemberProfileEditorProps` - 主组件 Props 类型
- `EditablePublication` - 可编辑出版物类型
- `EditableProjectInfo` - 可编辑项目信息类型
- `SectionId` - 区域 ID 联合类型
- `AvatarState` - 头像相关状态类型
- `ModalStates` - 模态框状态类型
- `EditingStates` - 编辑状态类型
- `DataListStates` - 数据列表状态类型
- `LoadingStates` - 加载状态类型
- `BasicFieldStates` - 基本字段状态类型
- `SectionStates` - 区域展开状态类型

### 2. utils.ts - 工具函数模块

**职责**: 包含纯函数和业务逻辑处理，实现业务逻辑与 UI 分离

**主要函数**:
- `formatStatusLabel()` - 格式化成员状态标签
- `validateAvatarFile()` - 验证头像文件类型和大小
- `createAvatarPreview()` - 创建头像预览 URL
- `isValidMemberStatus()` - 验证成员状态值
- `initializeEditablePublications()` - 初始化可编辑出版物列表
- `processInputValue()` - 处理输入值
- `validateNumberInput()` - 验证数字输入
- `formatDisplayValue()` - 格式化显示值
- `createFeaturedPublicationsUpdateData()` - 创建特色出版物更新数据
- `findDragItemIndex()` - 查找拖拽项目索引
- `validateDragIndices()` - 验证拖拽索引
- `parseZodError()` - 解析 Zod 验证错误
- `getDefaultSectionStates()` - 获取默认区域展开状态

### 3. queries.ts - 数据查询模块

**职责**: 封装所有与后端交互的函数，实现数据层与 UI 层分离

**主要功能**:
- `updateMemberFieldQuery()` - 更新成员字段
- `updateMemberStatusQuery()` - 更新成员状态
- `updateMemberVisibilityQuery()` - 更新成员资料可见性
- `uploadAvatarQuery()` - 上传头像文件
- `updateFeaturedPublicationsQuery()` - 更新特色出版物
- `educationQueries` - 教育记录 CRUD 操作
- `awardQueries` - 获奖记录 CRUD 操作
- `teachingQueries` - 教学记录 CRUD 操作
- `projectQueries` - 项目记录 CRUD 操作
- `academicServiceQueries` - 学术服务记录 CRUD 操作
- `presentationQueries` - 演讲记录 CRUD 操作
- `softwareDatasetQueries` - 软件与数据集记录 CRUD 操作

### 4. handlers.ts - 事件处理模块

**职责**: 包含所有事件处理逻辑，实现事件处理与 UI 组件分离

**主要函数**:
- `createHandlers()` - 主要事件处理函数工厂
- `createTeachingHandlers()` - 教学记录处理函数工厂
- `createProjectHandlers()` - 项目记录处理函数工厂
- `createAcademicServiceHandlers()` - 学术服务记录处理函数工厂
- `createPresentationHandlers()` - 演讲记录处理函数工厂
- `createSoftwareDatasetHandlers()` - 软件与数据集记录处理函数工厂

### 5. components/EditableTextField.tsx - 可编辑组件

**职责**: 提供内联编辑功能的通用文本字段组件

**特性**:
- 支持文本输入和文本域两种模式
- 完整的编辑状态管理
- 数据验证和错误处理
- 使用主题颜色系统
- 完整的中文注释

### 6. index.ts - 统一导出

**职责**: 提供模块的统一导出接口，保持向后兼容性

**导出内容**:
- 所有类型定义
- 所有工具函数
- 所有查询函数
- 所有事件处理函数
- 所有组件
- 重新导出的外部类型

## 🎯 使用方式

### 导入重构后的组件

```typescript
// 替换原来的导入
import MemberProfileEditor from "./MemberProfileEditor";

// 使用重构版本
import MemberProfileEditor from "./MemberProfileEditor_Refactored";
```

### 导入特定模块

```typescript
// 导入工具函数
import { formatStatusLabel, validateAvatarFile } from "./memberProfileEditor";

// 导入查询函数
import { educationQueries, updateMemberFieldQuery } from "./memberProfileEditor";

// 导入类型
import type { EditablePublication, SectionId } from "./memberProfileEditor";

// 导入组件
import { EditableTextField } from "./memberProfileEditor";
```

## ✅ 重构优势

### 1. 模块化架构
- **关注点分离**: 每个模块职责明确
- **信息局部化**: 相关功能组织在一起
- **易于维护**: 问题定位和修改更容易

### 2. 代码质量提升
- **类型安全**: 完整的 TypeScript 类型定义
- **错误处理**: 统一的错误处理模式
- **代码复用**: 工具函数和组件可复用

### 3. 开发体验改善
- **主题一致性**: 统一使用 `theme.ts` 颜色定义
- **文档完整**: 详细的中文注释和说明
- **测试支持**: 包含模块测试文件

### 4. 兼容性保证
- **接口不变**: 组件 props 接口完全不变
- **功能完整**: 保持所有原有功能
- **依赖兼容**: 与现有子组件完全兼容

## 🧪 测试

运行模块测试：

```typescript
import { runAllTests } from "./memberProfileEditor/test";

// 运行所有测试
const testResult = runAllTests();
console.log("测试结果:", testResult);
```

## 📝 注意事项

1. **颜色使用**: 严格使用 `themeColors` 定义，避免硬编码
2. **类型安全**: 避免使用 `any` 类型，保持类型检查
3. **错误处理**: 遵循项目统一的错误处理和 toast 通知模式
4. **命名约定**: 组件使用 PascalCase，函数使用 camelCase
5. **注释规范**: 所有函数和类型都应有完整的中文 JSDoc 注释

## 🔄 后续扩展

如需添加新功能：

1. **新类型**: 在 `types.ts` 中定义
2. **新工具函数**: 在 `utils.ts` 中添加
3. **新查询函数**: 在 `queries.ts` 中添加
4. **新事件处理**: 在 `handlers.ts` 中添加
5. **新组件**: 在 `components/` 目录中创建
6. **导出更新**: 在 `index.ts` 中添加导出

遵循现有的模块化模式，保持代码的一致性和可维护性。
